# cogs/multi_queues.py

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import config
from .match import MatchCog
from utils.team_validation import validate_player_for_queue, get_team_info_for_queue

# Dicionário para armazenar o estado das filas múltiplas
# Formato: { 'tipo_fila': { 'valor_da_fila': [id_do_jogador1, id_do_jogador2] } }
active_multi_queues = {
    "2v2": {},
    "3v3": {},
    "4v4": {},
    "5v5": {}
}

def get_multi_queue_embed(queue_type: str, value: int, players: list):
    """Cria o embed da fila específica."""
    queue_config = config.QUEUE_CHANNELS[queue_type]["config"]
    queue_info = queue_config[value]
    max_players = queue_info["max_players"]

    # Cores diferentes para cada tipo
    colors = {
        "2v2": discord.Color.blue(),
        "3v3": discord.Color.green(),
        "4v4": discord.Color.orange(),
        "5v5": discord.Color.purple()
    }

    embed = discord.Embed(
        title=f"[{queue_info['name']}]",
        description="⚠️ **Requisitos:** Rank registrado + Time completo (5 jogadores)\nClique no botão abaixo para entrar na fila.",
        color=colors.get(queue_type, discord.Color.blue())
    )

    if players:
        # Mostra informações dos times dos jogadores na fila
        from utils.team_validation import load_data, get_team_info_for_queue
        data = load_data()

        player_info = []
        for pid in players:
            player_data = data["players"].get(str(pid))
            if player_data and "team_id" in player_data:
                team_data = data["teams"].get(player_data["team_id"])
                team_info = get_team_info_for_queue(team_data)
                player_info.append(f"<@{pid}> - {team_info}")
            else:
                player_info.append(f"<@{pid}> - Time não encontrado")

        embed.add_field(name="Times na Fila:", value="\n".join(player_info), inline=False)
    else:
        embed.add_field(name="Times na Fila:", value="Nenhum time na fila.", inline=False)

    embed.add_field(name="Jogadores:", value=f"{len(players)}/{max_players}", inline=True)

    return embed

class MultiQueueButton(Button):
    def __init__(self, queue_type: str, value: int, label: str):
        super().__init__(label=label, style=discord.ButtonStyle.green, custom_id=f"multi_queue_{queue_type}_{value}")
        self.queue_type = queue_type
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user

        # Valida se o jogador pode entrar na fila (rank + time completo)
        can_join, error_message, team_data = validate_player_for_queue(user.id)
        if not can_join:
            await interaction.response.send_message(error_message, ephemeral=True)
            return

        # Verifica se o jogador já está em alguma fila deste tipo
        for queue_value, players in active_multi_queues[self.queue_type].items():
            if user.id in players:
                await interaction.response.send_message(f"Você já está na fila de {queue_value} R$ ({self.queue_type.upper()})!", ephemeral=True)
                return

        # Adiciona o jogador à fila
        if self.value not in active_multi_queues[self.queue_type]:
            active_multi_queues[self.queue_type][self.value] = []

        active_multi_queues[self.queue_type][self.value].append(user.id)
        players_in_queue = active_multi_queues[self.queue_type][self.value]

        # Atualiza a mensagem da fila
        embed = get_multi_queue_embed(self.queue_type, self.value, players_in_queue)
        await interaction.message.edit(embed=embed)

        # Mensagem de sucesso com info do time
        team_info = get_team_info_for_queue(team_data)
        await interaction.response.send_message(
            f"✅ Você entrou na fila de {self.value} R$ ({self.queue_type.upper()})!\n"
            f"**Time:** {team_info}",
            ephemeral=True
        )

        # Verifica se a fila está cheia
        queue_config = config.QUEUE_CHANNELS[self.queue_type]["config"]
        max_players = queue_config[self.value]["max_players"]
        if len(players_in_queue) >= max_players:
            # Cria a partida
            match_cog = self.view.bot.get_cog("MatchCog")
            if match_cog:
                await match_cog.create_match(interaction, players_in_queue, self.value)

                # Limpa a fila e atualiza a mensagem original
                active_multi_queues[self.queue_type][self.value].clear()
                new_embed = get_multi_queue_embed(self.queue_type, self.value, [])
                await interaction.message.edit(embed=new_embed)

class MultiLeaveQueueButton(Button):
    def __init__(self, queue_type: str, value: int, label: str):
        super().__init__(label=label, style=discord.ButtonStyle.gray, custom_id=f"multi_leave_{queue_type}_{value}")
        self.queue_type = queue_type
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user
        
        # Verifica se o jogador está na fila desta aposta
        if self.value not in active_multi_queues[self.queue_type] or user.id not in active_multi_queues[self.queue_type][self.value]:
            await interaction.response.send_message(f"Você não está nesta fila ({self.queue_type.upper()})!", ephemeral=True)
            return
        
        # Remove o jogador da fila
        active_multi_queues[self.queue_type][self.value].remove(user.id)
        
        # Atualiza a mensagem da fila
        players_in_queue = active_multi_queues[self.queue_type][self.value]
        embed = get_multi_queue_embed(self.queue_type, self.value, players_in_queue)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message(f"Você saiu da fila de {self.value} R$ ({self.queue_type.upper()})!", ephemeral=True)

class MultiQueueView(View):
    def __init__(self, bot: commands.Bot, queue_type: str, queue_value: int):
        super().__init__(timeout=None)
        self.bot = bot
        self.queue_type = queue_type
        self.queue_value = queue_value
        
        # Cria os botões para entrar e sair da fila
        self.add_item(MultiQueueButton(queue_type, queue_value, f"Entrar na fila (R${queue_value})"))
        self.add_item(MultiLeaveQueueButton(queue_type, queue_value, "Sair da fila"))

class MultiQueuesCog(commands.Cog, name="MultiQueuesCog"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        
        # Inicializa as filas para cada tipo
        for queue_type in active_multi_queues.keys():
            queue_config = config.QUEUE_CHANNELS[queue_type]["config"]
            for value in queue_config.keys():
                active_multi_queues[queue_type][value] = []

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra as Views persistentes para cada tipo e valor de fila
        if not hasattr(self.bot, '_multi_queue_views_registered'):
            for queue_type in config.QUEUE_CHANNELS.keys():
                queue_config = config.QUEUE_CHANNELS[queue_type]["config"]
                for value in queue_config.keys():
                    self.bot.add_view(MultiQueueView(self.bot, queue_type, value))
            self.bot._multi_queue_views_registered = True
        print("Multi Queue Cog is ready. Views registered.")

    @commands.command(name="setup_2v2")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_2v2_queues(self, ctx):
        """Configura as filas 2v2."""
        await self._setup_queue_type(ctx, "2v2", config.QUEUE_2V2_CHANNEL_ID)

    @commands.command(name="setup_3v3")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_3v3_queues(self, ctx):
        """Configura as filas 3v3."""
        await self._setup_queue_type(ctx, "3v3", config.QUEUE_3V3_CHANNEL_ID)

    @commands.command(name="setup_4v4")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_4v4_queues(self, ctx):
        """Configura as filas 4v4."""
        await self._setup_queue_type(ctx, "4v4", config.QUEUE_4V4_CHANNEL_ID)

    @commands.command(name="setup_5v5")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_5v5_queues(self, ctx):
        """Configura as filas 5v5."""
        await self._setup_queue_type(ctx, "5v5", config.QUEUE_5V5_CHANNEL_ID)

    async def _setup_queue_type(self, ctx, queue_type: str, channel_id: int):
        """Função auxiliar para configurar filas de um tipo específico."""
        channel = self.bot.get_channel(channel_id)
        if not channel:
            await ctx.send(f"Canal de filas {queue_type.upper()} não encontrado. Verifique o ID no config.py")
            return

        await ctx.send(f"Configurando as filas {queue_type.upper()}...", delete_after=5)
        
        # Limpa mensagens antigas
        try:
            await channel.purge(limit=50)
        except discord.HTTPException:
            pass

        # Cria uma mensagem para cada valor de fila
        queue_config = config.QUEUE_CHANNELS[queue_type]["config"]
        for value in queue_config.keys():
            embed = get_multi_queue_embed(queue_type, value, [])
            await channel.send(embed=embed, view=MultiQueueView(self.bot, queue_type, value))

        # Tenta deletar a mensagem do comando
        try:
            await ctx.message.delete()
        except discord.NotFound:
            pass

async def setup(bot: commands.Bot):
    await bot.add_cog(MultiQueuesCog(bot), guild=discord.Object(id=config.GUILD_ID))
