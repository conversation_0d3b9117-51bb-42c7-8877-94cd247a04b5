# cogs/rematch_queues.py

import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import But<PERSON>, View
import json
import os
from datetime import datetime
import config

# Arquivo separado para filas REMATCH
REMATCH_QUEUES_FILE = "rematch_queues_data.json"

def load_rematch_queues():
    if os.path.exists(REMATCH_QUEUES_FILE):
        with open(REMATCH_QUEUES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        "2v2": {"15": [], "25": [], "50": [], "100": []},
        "3v3": {"15": [], "25": [], "50": [], "100": []},
        "4v4": {"15": [], "25": [], "50": [], "100": []},
        "5v5": {"15": [], "25": [], "50": [], "100": []}
    }

def save_rematch_queues(data):
    with open(REMATCH_QUEUES_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

class RematchQueueButton(Button):
    def __init__(self, queue_type: str, value: str):
        super().__init__(
            label=f"Entrar na fila {queue_type} (R${value})",
            style=discord.ButtonStyle.danger,
            custom_id=f"rematch_queue_{queue_type}_{value}"
        )
        self.queue_type = queue_type
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        
        user_id = interaction.user.id
        
        # Verifica se tem rank REMATCH
        from cogs.rematch_ranks_teams import get_rematch_player_data
        player_data = get_rematch_player_data(user_id)
        
        if not player_data or "rank" not in player_data:
            await interaction.followup.send("❌ Você precisa registrar seu rank REMATCH primeiro!", ephemeral=True)
            return
        
        # Verifica se está em um time REMATCH
        if "team_id" not in player_data:
            await interaction.followup.send("❌ Você precisa estar em um time REMATCH para entrar na fila!", ephemeral=True)
            return
        
        # Carrega filas
        queues = load_rematch_queues()
        current_queue = queues[self.queue_type][self.value]
        
        # Verifica se já está na fila
        if user_id in current_queue:
            await interaction.followup.send(f"Você já está na fila {self.queue_type} de R${self.value} (REMATCH)!", ephemeral=True)
            return
        
        # Remove de outras filas primeiro
        for queue_type in queues:
            for queue_value in queues[queue_type]:
                if user_id in queues[queue_type][queue_value]:
                    queues[queue_type][queue_value].remove(user_id)
        
        # Adiciona na fila atual
        current_queue.append(user_id)
        save_rematch_queues(queues)
        
        # Verifica se a fila está cheia
        max_players = int(self.queue_type[0]) * 2  # 2v2 = 4, 3v3 = 6, etc.
        
        if len(current_queue) >= max_players:
            # Cria partida
            await self.create_rematch_match(interaction, current_queue[:max_players])
            
            # Remove jogadores da fila
            for player_id in current_queue[:max_players]:
                current_queue.remove(player_id)
            save_rematch_queues(queues)
        else:
            await interaction.followup.send(
                f"✅ Você entrou na fila {self.queue_type} de R${self.value} (REMATCH)!\n"
                f"Jogadores na fila: {len(current_queue)}/{max_players}",
                ephemeral=True
            )
        
        # Atualiza a mensagem da fila
        await self.update_queue_message(interaction)

    async def create_rematch_match(self, interaction, players):
        """Cria uma partida REMATCH."""
        try:
            from cogs.match import create_match_channel
            
            # Cria canal de aposta
            channel = await create_match_channel(
                interaction.guild,
                players,
                self.value,
                f"REMATCH {self.queue_type.upper()}"
            )
            
            if channel:
                # Notifica jogadores
                players_mentions = " ".join([f"<@{p}>" for p in players])
                
                embed = discord.Embed(
                    title=f"🔫 Partida REMATCH {self.queue_type.upper()} Criada!",
                    description=f"Valor da aposta: **R${self.value}**",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="👥 Jogadores",
                    value=players_mentions,
                    inline=False
                )
                embed.add_field(
                    name="📍 Canal da partida",
                    value=channel.mention,
                    inline=False
                )
                
                await interaction.followup.send(
                    f"🔫 **PARTIDA REMATCH {self.queue_type.upper()} ENCONTRADA!**\n{players_mentions}",
                    embed=embed
                )
                
        except Exception as e:
            print(f"Erro ao criar partida REMATCH: {e}")

    async def update_queue_message(self, interaction):
        """Atualiza a mensagem da fila."""
        try:
            queues = load_rematch_queues()
            
            # Pega dados dos times para mostrar nomes
            from cogs.rematch_ranks_teams import load_rematch_data
            rematch_data = load_rematch_data()
            
            embed = discord.Embed(
                title=f"🔫 Filas REMATCH {self.queue_type.upper()}",
                description="Filas de apostas REMATCH - Entre em um time primeiro!",
                color=discord.Color.red()
            )
            
            for value in ["15", "25", "50", "100"]:
                queue = queues[self.queue_type][value]
                max_players = int(self.queue_type[0]) * 2
                
                if queue:
                    # Mostra times na fila
                    teams_in_queue = []
                    for player_id in queue:
                        player_data = rematch_data["players"].get(str(player_id))
                        if player_data and "team_id" in player_data:
                            team_data = rematch_data["teams"].get(player_data["team_id"])
                            if team_data:
                                team_name = team_data["name"]
                                avg_rank = team_data.get("average_rank_name", "Sem rank")
                                teams_in_queue.append(f"{team_name} ({avg_rank})")
                    
                    queue_text = "\n".join(teams_in_queue) if teams_in_queue else "Nenhum time"
                else:
                    queue_text = "Vazia"
                
                embed.add_field(
                    name=f"💰 R${value} ({len(queue)}/{max_players})",
                    value=queue_text,
                    inline=True
                )
            
            embed.set_footer(text=f"Última atualização: {datetime.now().strftime('%H:%M:%S')}")
            
            # Atualiza a mensagem original
            await interaction.edit_original_response(embed=embed)
            
        except Exception as e:
            print(f"Erro ao atualizar mensagem da fila: {e}")

class RematchLeaveQueueButton(Button):
    def __init__(self, queue_type: str):
        super().__init__(
            label="Sair da fila",
            style=discord.ButtonStyle.secondary,
            custom_id=f"rematch_leave_queue_{queue_type}"
        )
        self.queue_type = queue_type

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        
        user_id = interaction.user.id
        queues = load_rematch_queues()
        
        # Remove de todas as filas deste tipo
        removed = False
        for value in queues[self.queue_type]:
            if user_id in queues[self.queue_type][value]:
                queues[self.queue_type][value].remove(user_id)
                removed = True
        
        save_rematch_queues(queues)
        
        if removed:
            await interaction.followup.send(f"✅ Você saiu da fila {self.queue_type} (REMATCH)!", ephemeral=True)
        else:
            await interaction.followup.send(f"❌ Você não estava em nenhuma fila {self.queue_type} (REMATCH)!", ephemeral=True)

class RematchQueueView(View):
    def __init__(self, queue_type: str):
        super().__init__(timeout=None)
        
        # Adiciona botões para cada valor
        for value in ["15", "25", "50", "100"]:
            self.add_item(RematchQueueButton(queue_type, value))
        
        # Adiciona botão para sair da fila
        self.add_item(RematchLeaveQueueButton(queue_type))

class RematchQueuesCog(commands.Cog, name="RematchQueues"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra views persistentes para cada tipo de fila
        if not hasattr(self.bot, '_rematch_queue_views_registered'):
            for queue_type in ["2v2", "3v3", "4v4", "5v5"]:
                self.bot.add_view(RematchQueueView(queue_type))
            self.bot._rematch_queue_views_registered = True
        print("Rematch Queues Cog is ready. Views registered.")

    @app_commands.command(name="setup_rematch_2v2", description="Configura fila REMATCH 2v2 (Staff)")
    async def setup_rematch_2v2(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "2v2")

    @app_commands.command(name="setup_rematch_3v3", description="Configura fila REMATCH 3v3 (Staff)")
    async def setup_rematch_3v3(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "3v3")

    @app_commands.command(name="setup_rematch_4v4", description="Configura fila REMATCH 4v4 (Staff)")
    async def setup_rematch_4v4(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "4v4")

    @app_commands.command(name="setup_rematch_5v5", description="Configura fila REMATCH 5v5 (Staff)")
    async def setup_rematch_5v5(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "5v5")

    async def setup_rematch_queue(self, interaction, queue_type: str):
        """Configura uma fila REMATCH específica."""
        await interaction.response.defer()
        
        max_players = int(queue_type[0]) * 2
        
        embed = discord.Embed(
            title=f"🔫 Filas REMATCH {queue_type.upper()}",
            description=f"Apostas {queue_type} - {max_players} jogadores\n*Você precisa estar em um time REMATCH!*",
            color=discord.Color.red()
        )
        
        # Mostra filas vazias inicialmente
        for value in ["15", "25", "50", "100"]:
            embed.add_field(
                name=f"💰 R${value} (0/{max_players})",
                value="Vazia",
                inline=True
            )
        
        embed.add_field(
            name="📋 Requisitos",
            value="• Ter rank REMATCH registrado\n• Estar em um time REMATCH\n• Time com 5 jogadores (para 5v5)",
            inline=False
        )
        
        embed.set_footer(text="Sistema de Filas REMATCH • WantedQueue")
        
        view = RematchQueueView(queue_type)
        await interaction.channel.send(embed=embed, view=view)
        
        await interaction.followup.send(f"✅ Fila REMATCH {queue_type} configurada!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchQueuesCog(bot), guild=discord.Object(id=config.GUILD_ID))
