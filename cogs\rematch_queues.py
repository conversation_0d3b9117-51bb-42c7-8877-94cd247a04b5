# cogs/rematch_queues.py

import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import But<PERSON>, View
import json
import os
from datetime import datetime
import config

# Arquivo separado para filas REMATCH
REMATCH_QUEUES_FILE = "rematch_queues_data.json"

def load_rematch_queues():
    if os.path.exists(REMATCH_QUEUES_FILE):
        with open(REMATCH_QUEUES_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        "2v2": {"5": [], "10": [], "25": [], "50": [], "100": []},
        "3v3": {"5": [], "10": [], "25": [], "50": [], "100": []},
        "4v4": {"5": [], "10": [], "25": [], "50": [], "100": []},
        "5v5": {"5": [], "10": [], "25": [], "50": [], "100": []}
    }

def save_rematch_queues(data):
    with open(REMATCH_QUEUES_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

class RematchQueueButton(Button):
    def __init__(self, queue_type: str, value: str):
        super().__init__(
            label=f"Entrar na fila {queue_type} (R${value})",
            style=discord.ButtonStyle.danger,
            custom_id=f"rematch_queue_{queue_type}_{value}"
        )
        self.queue_type = queue_type
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        user_id = interaction.user.id

        # Verifica se tem rank REMATCH
        from cogs.rematch_ranks_teams import get_rematch_player_data, get_rematch_team_data, load_rematch_data
        player_data = get_rematch_player_data(user_id)

        if not player_data or "rank" not in player_data:
            await interaction.followup.send("❌ Você precisa registrar seu rank REMATCH primeiro!", ephemeral=True)
            return

        # Verifica se está em um time REMATCH
        if "team_id" not in player_data:
            await interaction.followup.send("❌ Você precisa estar em um time REMATCH para entrar na fila!", ephemeral=True)
            return

        team_id = player_data["team_id"]
        team_data = get_rematch_team_data(team_id)

        if not team_data:
            await interaction.followup.send("❌ Erro: Time não encontrado.", ephemeral=True)
            return

        # Verifica número mínimo de membros (sempre 2, independente do tipo de fila)
        min_members = 2

        if len(team_data["members"]) < min_members:
            await interaction.followup.send(
                f"❌ Seu time precisa ter pelo menos {min_members} membros para entrar na fila!\n"
                f"Membros atuais: {len(team_data['members'])}/{min_members}",
                ephemeral=True
            )
            return

        # Verifica se todos os membros têm rank
        data = load_rematch_data()
        members_without_rank = []
        for member_id in team_data["members"]:
            member_data = data["players"].get(member_id)
            if not member_data or "rank" not in member_data:
                members_without_rank.append(f"<@{member_id}>")

        if members_without_rank:
            await interaction.followup.send(
                f"❌ Todos os membros do time precisam ter rank REMATCH registrado!\n"
                f"Membros sem rank: {', '.join(members_without_rank)}",
                ephemeral=True
            )
            return

        # Carrega filas
        queues = load_rematch_queues()
        current_queue = queues[self.queue_type][self.value]

        # Verifica se o time já está na fila
        if team_id in current_queue:
            await interaction.followup.send(f"Seu time já está na fila {self.queue_type} de R${self.value} (REMATCH)!", ephemeral=True)
            return

        # Remove time de outras filas primeiro
        for queue_type in queues:
            for queue_value in queues[queue_type]:
                if team_id in queues[queue_type][queue_value]:
                    queues[queue_type][queue_value].remove(team_id)

        # Adiciona o time na fila atual
        current_queue.append(team_id)
        save_rematch_queues(queues)

        # Verifica se a fila está cheia (2 times)
        if len(current_queue) >= 2:
            # Cria partida entre os 2 times
            await self.create_rematch_match(interaction, current_queue[:2])

            # Remove times da fila
            for team in current_queue[:2]:
                current_queue.remove(team)
            save_rematch_queues(queues)
        else:
            team_name = team_data["name"]
            avg_rank = team_data.get("average_rank_name", "Sem rank")

            await interaction.followup.send(
                f"✅ Time **{team_name}** entrou na fila {self.queue_type} de R${self.value} (REMATCH)!\n"
                f"Rank médio: {avg_rank}\n"
                f"Times na fila: {len(current_queue)}/2",
                ephemeral=True
            )

        # Atualiza a mensagem da fila
        await self.update_queue_message(interaction)

    async def create_rematch_match(self, interaction, teams):
        """Cria uma partida REMATCH entre 2 times."""
        try:
            from cogs.match import create_match_channel
            from cogs.rematch_ranks_teams import load_rematch_data

            data = load_rematch_data()
            all_players = []
            teams_info = []

            # Coleta todos os jogadores dos times
            for team_id in teams:
                team_data = data["teams"].get(team_id)
                if team_data:
                    # Pega membros necessários baseado no tipo de fila
                    members_needed = int(self.queue_type[0])  # 2v2 = 2, 3v3 = 3, etc.
                    team_members = team_data["members"][:members_needed]

                    all_players.extend([int(member_id) for member_id in team_members])

                    teams_info.append({
                        "name": team_data["name"],
                        "avg_rank": team_data.get("average_rank_name", "Sem rank"),
                        "members": team_members
                    })

            # Cria canal de aposta
            channel = await create_match_channel(
                interaction.guild,
                all_players,
                self.value,
                f"REMATCH {self.queue_type.upper()}"
            )

            if channel:
                # Cria embed com informações dos times
                embed = discord.Embed(
                    title=f"🔫 Partida REMATCH {self.queue_type.upper()} Criada!",
                    description=f"Valor da aposta: **R${self.value}**",
                    color=discord.Color.red()
                )

                # Adiciona informações de cada time
                for i, team_info in enumerate(teams_info, 1):
                    members_mentions = " ".join([f"<@{m}>" for m in team_info["members"]])
                    embed.add_field(
                        name=f"🔫 Time {i}: {team_info['name']}",
                        value=f"**Rank médio:** {team_info['avg_rank']}\n**Jogadores:** {members_mentions}",
                        inline=False
                    )

                embed.add_field(
                    name="📍 Canal da partida",
                    value=channel.mention,
                    inline=False
                )

                # Notifica todos os jogadores
                all_mentions = " ".join([f"<@{p}>" for p in all_players])

                await interaction.followup.send(
                    f"🔫 **PARTIDA REMATCH {self.queue_type.upper()} ENCONTRADA!**\n{all_mentions}",
                    embed=embed
                )

        except Exception as e:
            print(f"Erro ao criar partida REMATCH: {e}")

    async def update_queue_message(self, interaction):
        """Atualiza a mensagem da fila."""
        try:
            queues = load_rematch_queues()

            # Pega dados dos times para mostrar nomes
            from cogs.rematch_ranks_teams import load_rematch_data
            rematch_data = load_rematch_data()

            embed = discord.Embed(
                title=f"🔫 Filas REMATCH {self.queue_type.upper()}",
                description=f"Filas de apostas REMATCH - Qualquer membro pode entrar!\n*Mínimo: 2 membros no time*",
                color=discord.Color.red()
            )

            # Cria texto das filas com quebras de linha e espaçamento
            filas_text = ""
            for i, value in enumerate(["5", "10", "25", "50", "100"]):
                queue = queues[self.queue_type][value]

                if queue:
                    # Mostra times na fila
                    teams_in_queue = []
                    for team_id in queue:
                        team_data = rematch_data["teams"].get(team_id)
                        if team_data:
                            team_name = team_data["name"]
                            avg_rank = team_data.get("average_rank_name", "Sem rank")
                            teams_in_queue.append(f"**{team_name}** ({avg_rank})")

                    queue_text = "\n".join(teams_in_queue) if teams_in_queue else "Vazia"
                else:
                    queue_text = "Vazia"

                # Adiciona a fila com espaçamento
                filas_text += f"💰 **R${value}** ({len(queue)}/2 times)\n{queue_text}"

                # Adiciona espaçamento entre filas (exceto na última)
                if i < 4:
                    filas_text += "\n\n\n"

            embed.add_field(
                name="📊 Estado das Filas",
                value=filas_text,
                inline=False
            )

            embed.set_footer(text=f"Última atualização: {datetime.now().strftime('%H:%M:%S')}")

            # Atualiza a mensagem original
            await interaction.edit_original_response(embed=embed)

        except Exception as e:
            print(f"Erro ao atualizar mensagem da fila: {e}")

class RematchLeaveQueueButton(Button):
    def __init__(self, queue_type: str):
        super().__init__(
            label="Sair da fila",
            style=discord.ButtonStyle.secondary,
            custom_id=f"rematch_leave_queue_{queue_type}"
        )
        self.queue_type = queue_type

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        user_id = interaction.user.id

        # Verifica se está em um time REMATCH
        from cogs.rematch_ranks_teams import get_rematch_player_data, get_rematch_team_data
        player_data = get_rematch_player_data(user_id)

        if not player_data or "team_id" not in player_data:
            await interaction.followup.send("❌ Você não está em nenhum time REMATCH.", ephemeral=True)
            return

        team_id = player_data["team_id"]
        team_data = get_rematch_team_data(team_id)

        if not team_data:
            await interaction.followup.send("❌ Erro: Time não encontrado.", ephemeral=True)
            return

        # Qualquer membro pode remover o time da fila

        queues = load_rematch_queues()

        # Remove time de todas as filas deste tipo
        removed = False
        for value in queues[self.queue_type]:
            if team_id in queues[self.queue_type][value]:
                queues[self.queue_type][value].remove(team_id)
                removed = True

        save_rematch_queues(queues)

        if removed:
            team_name = team_data["name"]
            await interaction.followup.send(f"✅ Time **{team_name}** saiu da fila {self.queue_type} (REMATCH)!", ephemeral=True)
        else:
            await interaction.followup.send(f"❌ Seu time não estava em nenhuma fila {self.queue_type} (REMATCH)!", ephemeral=True)

        # Atualiza a mensagem da fila
        try:
            # Usa a mesma função de atualização do botão principal
            button = RematchQueueButton(self.queue_type, "5")  # Valor não importa para atualização
            await button.update_queue_message(interaction)
        except:
            pass  # Se não conseguir atualizar, ignora

class RematchQueueView(View):
    def __init__(self, queue_type: str):
        super().__init__(timeout=None)
        
        # Adiciona botões para cada valor
        for value in ["5", "10", "25", "50", "100"]:
            self.add_item(RematchQueueButton(queue_type, value))
        
        # Adiciona botão para sair da fila
        self.add_item(RematchLeaveQueueButton(queue_type))

class RematchQueuesCog(commands.Cog, name="RematchQueues"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra views persistentes para cada tipo de fila
        if not hasattr(self.bot, '_rematch_queue_views_registered'):
            for queue_type in ["2v2", "3v3", "4v4", "5v5"]:
                self.bot.add_view(RematchQueueView(queue_type))
            self.bot._rematch_queue_views_registered = True
        print("Rematch Queues Cog is ready. Views registered.")

    @app_commands.command(name="setup_rematch_2v2", description="Configura fila REMATCH 2v2 (Staff)")
    async def setup_rematch_2v2(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "2v2")

    @app_commands.command(name="setup_rematch_3v3", description="Configura fila REMATCH 3v3 (Staff)")
    async def setup_rematch_3v3(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "3v3")

    @app_commands.command(name="setup_rematch_4v4", description="Configura fila REMATCH 4v4 (Staff)")
    async def setup_rematch_4v4(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "4v4")

    @app_commands.command(name="setup_rematch_5v5", description="Configura fila REMATCH 5v5 (Staff)")
    async def setup_rematch_5v5(self, interaction: discord.Interaction):
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await self.setup_rematch_queue(interaction, "5v5")

    async def setup_rematch_queue(self, interaction, queue_type: str):
        """Configura uma fila REMATCH específica."""
        await interaction.response.defer()

        min_members = int(queue_type[0])  # 2v2 = 2, 3v3 = 3, etc.

        embed = discord.Embed(
            title=f"🔫 Filas REMATCH {queue_type.upper()}",
            description=f"Apostas {queue_type} - 2 times\n*Qualquer membro pode entrar na fila!*",
            color=discord.Color.red()
        )

        # Cria texto das filas com quebras de linha
        filas_text = ""
        for i, value in enumerate(["5", "10", "25", "50", "100"]):
            filas_text += f"💰 **R${value}** (0/2 times)\nVazia"

            # Adiciona espaçamento entre filas (exceto na última)
            if i < 4:
                filas_text += "\n\n\n"

        embed.add_field(
            name="📊 Estado das Filas",
            value=filas_text,
            inline=False
        )

        embed.add_field(
            name="📋 Requisitos",
            value="• Estar em um time REMATCH\n• Time com mínimo 2 membros\n• Todos os membros com rank registrado",
            inline=False
        )

        embed.add_field(
            name="🎯 Como funciona",
            value="• Qualquer membro clica no botão da fila\n• Sistema verifica requisitos\n• Mostra nome do time + rank médio\n• 2 times = partida criada!",
            inline=False
        )

        embed.set_footer(text="Sistema de Filas REMATCH • WantedQueue")

        view = RematchQueueView(queue_type)
        await interaction.channel.send(embed=embed, view=view)

        await interaction.followup.send(f"✅ Fila REMATCH {queue_type} configurada!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchQueuesCog(bot), guild=discord.Object(id=config.GUILD_ID))
