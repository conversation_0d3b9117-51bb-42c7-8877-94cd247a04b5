# cogs/team_ranking.py

import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime
import config

# Configurações
RANKING_CHANNEL_ID = config.RANKING_CHANNEL_ID
RANKING_FILE = "team_ranking_data.json"

def load_ranking_data():
    if os.path.exists(RANKING_FILE):
        with open(RANKING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_ranking_data(data):
    with open(RANKING_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def get_team_ranking_data(team_id: str):
    """Pega dados de ranking de um time específico."""
    data = load_ranking_data()
    if team_id not in data:
        data[team_id] = {
            "wins": 0,
            "losses": 0,
            "draws": 0,
            "points": 0,
            "current_streak": 0,  # Positivo = vitórias, Negativo = derrotas
            "streak_type": "none",  # "win", "loss", "draw", "none"
            "last_match": None,
            "total_matches": 0
        }
        save_ranking_data(data)
    return data[team_id]

def update_team_stats(team_id: str, result: str):
    """Atualiza estatísticas de um time após uma partida.
    result: 'win', 'loss', 'draw'
    """
    data = load_ranking_data()
    team_stats = get_team_ranking_data(team_id)
    
    # Atualiza estatísticas básicas
    if result == "win":
        team_stats["wins"] += 1
        team_stats["points"] += 3
        # Atualiza sequência
        if team_stats["streak_type"] == "win":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "win"
            
    elif result == "loss":
        team_stats["losses"] += 1
        # Pontos não mudam em derrota
        # Atualiza sequência
        if team_stats["streak_type"] == "loss":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "loss"
            
    elif result == "draw":
        team_stats["draws"] += 1
        team_stats["points"] += 1
        # Atualiza sequência
        if team_stats["streak_type"] == "draw":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "draw"
    
    team_stats["total_matches"] += 1
    team_stats["last_match"] = datetime.now().isoformat()
    
    data[team_id] = team_stats
    save_ranking_data(data)
    
    return team_stats

def calculate_win_rate(team_stats):
    """Calcula taxa de vitória."""
    total = team_stats["total_matches"]
    if total == 0:
        return 0.0
    return (team_stats["wins"] / total) * 100

def get_ranking_table():
    """Gera tabela de ranking ordenada por pontos."""
    data = load_ranking_data()
    
    # Filtra times com pelo menos 2 jogos
    qualified_teams = {}
    for team_id, stats in data.items():
        if stats["total_matches"] >= 2:
            qualified_teams[team_id] = stats
    
    # Ordena por pontos (desc), depois por taxa de vitória (desc)
    sorted_teams = sorted(
        qualified_teams.items(),
        key=lambda x: (x[1]["points"], calculate_win_rate(x[1])),
        reverse=True
    )
    
    return sorted_teams

async def update_ranking_channel(bot):
    """Atualiza a mensagem de ranking no canal."""
    try:
        channel = bot.get_channel(RANKING_CHANNEL_ID)
        if not channel:
            return
        
        # Pega dados dos times
        from cogs.ranks_teams import load_data
        teams_data = load_data()
        
        ranking = get_ranking_table()
        
        if not ranking:
            embed = discord.Embed(
                title="🏆 Ranking de Times",
                description="Nenhum time qualificado ainda.\n*Mínimo: 2 partidas jogadas*",
                color=discord.Color.gold()
            )
        else:
            embed = discord.Embed(
                title="🏆 Ranking de Times - Valorant",
                description="*Atualizado automaticamente após cada partida*",
                color=discord.Color.gold()
            )
            
            # Adiciona top 10
            ranking_text = ""
            for i, (team_id, stats) in enumerate(ranking[:10], 1):
                # Pega nome do time
                team_name = teams_data["teams"].get(team_id, {}).get("name", "Time Desconhecido")
                
                # Emoji de posição
                if i == 1:
                    pos_emoji = "🥇"
                elif i == 2:
                    pos_emoji = "🥈"
                elif i == 3:
                    pos_emoji = "🥉"
                else:
                    pos_emoji = f"{i}º"
                
                # Emoji de sequência
                if stats["streak_type"] == "win" and stats["current_streak"] >= 2:
                    streak_emoji = f"🔥{stats['current_streak']}W"
                elif stats["streak_type"] == "loss" and stats["current_streak"] >= 2:
                    streak_emoji = f"❄️{stats['current_streak']}L"
                else:
                    streak_emoji = ""
                
                win_rate = calculate_win_rate(stats)
                
                ranking_text += (
                    f"{pos_emoji} **{team_name}** {streak_emoji}\n"
                    f"📊 {stats['points']} pts • {stats['wins']}V-{stats['losses']}D-{stats['draws']}E • {win_rate:.1f}%\n\n"
                )
            
            embed.add_field(name="📈 Classificação", value=ranking_text, inline=False)
            
            embed.add_field(
                name="📋 Sistema de Pontuação",
                value="🏆 **Vitória:** +3 pontos\n🤝 **Empate:** +1 ponto\n💔 **Derrota:** 0 pontos",
                inline=True
            )
            
            embed.add_field(
                name="📊 Critérios",
                value="• Mínimo 2 partidas\n• Ordenado por pontos\n• Desempate por taxa de vitória",
                inline=True
            )
        
        embed.set_footer(text=f"Última atualização: {datetime.now().strftime('%d/%m/%Y às %H:%M')}")
        
        # Procura mensagem existente ou cria nova
        messages = []
        async for message in channel.history(limit=50):
            if message.author == bot.user and message.embeds:
                if "Ranking de Times" in message.embeds[0].title:
                    messages.append(message)
        
        if messages:
            # Atualiza a mensagem mais recente
            await messages[0].edit(embed=embed)
        else:
            # Cria nova mensagem
            await channel.send(embed=embed)
            
    except Exception as e:
        print(f"Erro ao atualizar ranking: {e}")

class TeamRankingCog(commands.Cog, name="TeamRanking"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="registrar_resultado", description="Registra resultado de uma partida (Staff)")
    @app_commands.describe(
        time_vencedor="Time que venceu a partida",
        time_perdedor="Time que perdeu a partida",
        empate="Se foi empate (deixe vazio se houve vencedor)"
    )
    async def register_result(
        self, 
        interaction: discord.Interaction, 
        time_vencedor: str = None,
        time_perdedor: str = None,
        empate: bool = False
    ):
        # Verifica se é staff
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode registrar resultados.", ephemeral=True)
            return
        
        await interaction.response.defer(ephemeral=True)
        
        # Pega dados dos times
        from cogs.ranks_teams import load_data
        teams_data = load_data()
        
        if empate:
            if not time_vencedor or not time_perdedor:
                await interaction.followup.send("❌ Para empate, informe ambos os times.", ephemeral=True)
                return
            
            # Procura IDs dos times
            winner_id = None
            loser_id = None
            
            for team_id, team_data in teams_data["teams"].items():
                if team_data["name"].lower() == time_vencedor.lower():
                    winner_id = team_id
                if team_data["name"].lower() == time_perdedor.lower():
                    loser_id = team_id
            
            if not winner_id or not loser_id:
                await interaction.followup.send("❌ Um ou ambos os times não foram encontrados.", ephemeral=True)
                return
            
            # Registra empate para ambos
            update_team_stats(winner_id, "draw")
            update_team_stats(loser_id, "draw")
            
            embed = discord.Embed(
                title="🤝 Empate Registrado!",
                description=f"**{time_vencedor}** 🆚 **{time_perdedor}**\nAmbos receberam +1 ponto",
                color=discord.Color.blue()
            )
            
        else:
            if not time_vencedor or not time_perdedor:
                await interaction.followup.send("❌ Informe o time vencedor e perdedor.", ephemeral=True)
                return
            
            # Procura IDs dos times
            winner_id = None
            loser_id = None
            
            for team_id, team_data in teams_data["teams"].items():
                if team_data["name"].lower() == time_vencedor.lower():
                    winner_id = team_id
                if team_data["name"].lower() == time_perdedor.lower():
                    loser_id = team_id
            
            if not winner_id or not loser_id:
                await interaction.followup.send("❌ Um ou ambos os times não foram encontrados.", ephemeral=True)
                return
            
            # Registra resultado
            update_team_stats(winner_id, "win")
            update_team_stats(loser_id, "loss")
            
            embed = discord.Embed(
                title="🏆 Resultado Registrado!",
                description=f"**{time_vencedor}** venceu **{time_perdedor}**\n+3 pontos para o vencedor",
                color=discord.Color.green()
            )
        
        await interaction.followup.send(embed=embed, ephemeral=True)
        
        # Atualiza canal de ranking
        await update_ranking_channel(self.bot)

    @app_commands.command(name="ranking", description="Mostra o ranking atual de times")
    async def show_ranking(self, interaction: discord.Interaction):
        await interaction.response.defer()
        
        # Força atualização do canal
        await update_ranking_channel(self.bot)
        
        await interaction.followup.send("✅ Ranking atualizado!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(TeamRankingCog(bot), guild=discord.Object(id=config.GUILD_ID))
