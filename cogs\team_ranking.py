# cogs/team_ranking.py

import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime
import config

# Configurações
RANKING_CHANNEL_ID = config.RANKING_CHANNEL_ID
RANKING_FILE = "team_ranking_data.json"

def load_ranking_data():
    if os.path.exists(RANKING_FILE):
        with open(RANKING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_ranking_data(data):
    with open(RANKING_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def get_team_ranking_data(team_id: str):
    """Pega dados de ranking de um time específico."""
    data = load_ranking_data()
    if team_id not in data:
        data[team_id] = {
            "wins": 0,
            "losses": 0,
            "points": 0,
            "current_streak": 0,  # Positivo = vitórias, Negativo = derrotas
            "streak_type": "none",  # "win", "loss", "none"
            "last_match": None,
            "total_matches": 0
        }
        save_ranking_data(data)
    return data[team_id]

def update_team_stats(team_id: str, result: str):
    """Atualiza estatísticas de um time após uma partida.
    result: 'win', 'loss'
    """
    data = load_ranking_data()
    team_stats = get_team_ranking_data(team_id)

    # Atualiza estatísticas básicas
    if result == "win":
        team_stats["wins"] += 1
        team_stats["points"] += 3
        # Atualiza sequência
        if team_stats["streak_type"] == "win":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "win"

    elif result == "loss":
        team_stats["losses"] += 1
        # Pontos não mudam em derrota
        # Atualiza sequência
        if team_stats["streak_type"] == "loss":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "loss"

    team_stats["total_matches"] += 1
    team_stats["last_match"] = datetime.now().isoformat()

    data[team_id] = team_stats
    save_ranking_data(data)

    return team_stats

def find_teams_from_players(players_list):
    """Encontra times dos jogadores na partida."""
    from cogs.ranks_teams import load_data
    teams_data = load_data()

    found_teams = {}

    for player_id in players_list:
        player_str = str(player_id)
        player_data = teams_data["players"].get(player_str)

        if player_data and "team_id" in player_data:
            team_id = player_data["team_id"]
            team_info = teams_data["teams"].get(team_id)

            if team_info:
                if team_id not in found_teams:
                    found_teams[team_id] = {
                        "name": team_info["name"],
                        "players": []
                    }
                found_teams[team_id]["players"].append(player_id)

    return found_teams

def calculate_win_rate(team_stats):
    """Calcula taxa de vitória."""
    total = team_stats["total_matches"]
    if total == 0:
        return 0.0
    return (team_stats["wins"] / total) * 100

def get_ranking_table():
    """Gera tabela de ranking ordenada por pontos."""
    data = load_ranking_data()
    
    # Filtra times com pelo menos 2 jogos
    qualified_teams = {}
    for team_id, stats in data.items():
        if stats["total_matches"] >= 2:
            qualified_teams[team_id] = stats
    
    # Ordena por pontos (desc), depois por taxa de vitória (desc)
    sorted_teams = sorted(
        qualified_teams.items(),
        key=lambda x: (x[1]["points"], calculate_win_rate(x[1])),
        reverse=True
    )
    
    return sorted_teams

async def update_ranking_channel(bot):
    """Atualiza a mensagem de ranking no canal."""
    try:
        channel = bot.get_channel(RANKING_CHANNEL_ID)
        if not channel:
            return
        
        # Pega dados dos times
        from cogs.ranks_teams import load_data
        teams_data = load_data()
        
        ranking = get_ranking_table()
        
        if not ranking:
            embed = discord.Embed(
                title="🏆 Ranking de Times",
                description="Nenhum time qualificado ainda.\n*Mínimo: 2 partidas jogadas*",
                color=discord.Color.gold()
            )
        else:
            embed = discord.Embed(
                title="🏆 Ranking de Times - Valorant",
                description="*Atualizado automaticamente após cada partida*",
                color=discord.Color.gold()
            )
            
            # Adiciona top 10
            ranking_text = ""
            for i, (team_id, stats) in enumerate(ranking[:10], 1):
                # Pega nome do time
                team_name = teams_data["teams"].get(team_id, {}).get("name", "Time Desconhecido")
                
                # Emoji de posição
                if i == 1:
                    pos_emoji = "🥇"
                elif i == 2:
                    pos_emoji = "🥈"
                elif i == 3:
                    pos_emoji = "🥉"
                else:
                    pos_emoji = f"{i}º"
                
                # Emoji de sequência
                if stats["streak_type"] == "win" and stats["current_streak"] >= 2:
                    streak_emoji = f"🔥{stats['current_streak']}W"
                elif stats["streak_type"] == "loss" and stats["current_streak"] >= 2:
                    streak_emoji = f"❄️{stats['current_streak']}L"
                else:
                    streak_emoji = ""
                
                win_rate = calculate_win_rate(stats)

                ranking_text += (
                    f"{pos_emoji} **{team_name}** {streak_emoji}\n"
                    f"📊 {stats['points']} pts • {stats['wins']}V-{stats['losses']}D • {win_rate:.1f}%\n\n"
                )
            
            embed.add_field(name="📈 Classificação", value=ranking_text, inline=False)
            
            embed.add_field(
                name="📋 Sistema de Pontuação",
                value="🏆 **Vitória:** +3 pontos\n💔 **Derrota:** 0 pontos",
                inline=True
            )
            
            embed.add_field(
                name="📊 Critérios",
                value="• Mínimo 2 partidas\n• Ordenado por pontos\n• Desempate por taxa de vitória",
                inline=True
            )
        
        embed.set_footer(text=f"Última atualização: {datetime.now().strftime('%d/%m/%Y às %H:%M')}")
        
        # Procura mensagem existente ou cria nova
        messages = []
        async for message in channel.history(limit=50):
            if message.author == bot.user and message.embeds:
                if "Ranking de Times" in message.embeds[0].title:
                    messages.append(message)
        
        if messages:
            # Atualiza a mensagem mais recente
            await messages[0].edit(embed=embed)
        else:
            # Cria nova mensagem
            await channel.send(embed=embed)
            
    except Exception as e:
        print(f"Erro ao atualizar ranking: {e}")

async def process_match_result(bot, players_list, bet_value):
    """Processa resultado da partida e atualiza ranking.
    Chamada pelo sistema de apostas após finalizar.
    """
    try:
        # Encontra times dos jogadores
        teams_found = find_teams_from_players(players_list)

        if len(teams_found) == 2:
            # Exatamente 2 times encontrados - perfeito!
            team_ids = list(teams_found.keys())
            team_names = [teams_found[tid]["name"] for tid in team_ids]

            print(f"🏆 RANKING: Partida detectada entre times {team_names[0]} vs {team_names[1]}")
            print(f"💰 Valor da aposta: {bet_value}")

            # Aqui você vai definir qual time ganhou
            # Por enquanto, vou deixar um placeholder
            # Isso será integrado com o modal de finalização

            return {
                "teams_detected": True,
                "teams": teams_found,
                "team_ids": team_ids,
                "team_names": team_names
            }
        else:
            print(f"⚠️ RANKING: {len(teams_found)} times detectados. Esperado: 2")
            return {
                "teams_detected": False,
                "teams_count": len(teams_found),
                "teams": teams_found
            }

    except Exception as e:
        print(f"❌ RANKING: Erro ao processar resultado: {e}")
        return {"teams_detected": False, "error": str(e)}

def register_team_victory(winner_team_id, loser_team_id, bet_value):
    """Registra vitória/derrota dos times e atualiza ranking."""
    try:
        # Atualiza estatísticas
        update_team_stats(winner_team_id, "win")
        update_team_stats(loser_team_id, "loss")

        # Pega nomes dos times
        from cogs.ranks_teams import load_data
        teams_data = load_data()

        winner_name = teams_data["teams"].get(winner_team_id, {}).get("name", "Time Desconhecido")
        loser_name = teams_data["teams"].get(loser_team_id, {}).get("name", "Time Desconhecido")

        print(f"🏆 RANKING ATUALIZADO: {winner_name} venceu {loser_name} (Aposta: {bet_value})")

        return {
            "success": True,
            "winner_name": winner_name,
            "loser_name": loser_name
        }

    except Exception as e:
        print(f"❌ Erro ao registrar vitória: {e}")
        return {"success": False, "error": str(e)}

class TeamRankingCog(commands.Cog, name="TeamRanking"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="ranking", description="Mostra o ranking atual de times")
    async def show_ranking(self, interaction: discord.Interaction):
        await interaction.response.defer()
        
        # Força atualização do canal
        await update_ranking_channel(self.bot)
        
        await interaction.followup.send("✅ Ranking atualizado!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(TeamRankingCog(bot), guild=discord.Object(id=config.GUILD_ID))
