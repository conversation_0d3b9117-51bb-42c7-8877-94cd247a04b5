# cogs/queues.py

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import config
from .match import MatchCog # Importamos o Cog de partida para chamá-lo

# Dicionário para armazenar o estado das filas em memória
# Formato: { 'valor_da_fila': [id_do_jogador1, id_do_jogador2] }
# Ex: { 5: [12345], 50: [] }
active_queues = {value: [] for value in config.QUEUE_CONFIG.keys()}

def get_queue_embed(value: int, players: list):
    """Cria o embed da fila."""
    queue_info = config.QUEUE_CONFIG[value]
    embed = discord.Embed(
        title=f"[{queue_info['name']}]",
        description="Clique no botão abaixo para entrar na fila.",
        color=discord.Color.blue()
    )
    player_mentions = "\n".join([f"<@{player_id}>" for player_id in players]) if players else "Nenhum jogador na fila."
    embed.add_field(name="Jogadores na Fila:", value=player_mentions, inline=False)
    embed.set_footer(text=f"Jogadores: {len(players)}/{queue_info['max_players']}")
    return embed

class QueueView(View):
    def __init__(self, bot: commands.Bot, queue_value: int):
        super().__init__(timeout=None) # Timeout=None torna a view persistente
        self.bot = bot
        self.queue_value = queue_value

        # Cria os botões para entrar e sair da fila
        self.add_item(QueueButton(value=queue_value, label=f"Entrar na fila (R${queue_value})"))
        self.add_item(LeaveQueueButton(value=queue_value, label="Sair da fila"))

class QueueButton(Button):
    def __init__(self, value: int, label: str):
        # O custom_id é crucial para o bot saber qual botão foi clicado após uma reinicialização
        super().__init__(label=label, style=discord.ButtonStyle.green, custom_id=f"queue_button_{value}")
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user
        queue_info = config.QUEUE_CONFIG[self.value]

        # Verifica se o jogador já está em alguma fila
        for queue_players in active_queues.values():
            if user.id in queue_players:
                await interaction.response.send_message("Você já está em uma fila! Saia da fila atual para entrar em outra.", ephemeral=True)
                return

        # Adiciona o jogador à fila
        active_queues[self.value].append(user.id)
        
        # Atualiza a mensagem da fila com o novo jogador
        players_in_queue = active_queues[self.value]
        embed = get_queue_embed(self.value, players_in_queue)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message(f"Você entrou na fila de {self.value} R$!", ephemeral=True)
        
        # Verifica se a fila está cheia
        if len(players_in_queue) == queue_info['max_players']:
            # Pega o Cog de Partidas para chamar a função de criação
            match_cog = self.view.bot.get_cog("MatchCog")
            if match_cog:
                # Cria a partida
                await match_cog.create_match(interaction, players_in_queue, self.value)
                
                # Limpa a fila e atualiza a mensagem original
                active_queues[self.value].clear()
                new_embed = get_queue_embed(self.value, [])
                await interaction.message.edit(embed=new_embed)

class LeaveQueueButton(Button):
    def __init__(self, value: int, label: str):
        # O custom_id é crucial para o bot saber qual botão foi clicado após uma reinicialização
        super().__init__(label=label, style=discord.ButtonStyle.red, custom_id=f"leave_queue_button_{value}")
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user

        # Verifica se o jogador está na fila desta aposta
        if user.id not in active_queues[self.value]:
            await interaction.response.send_message("Você não está nesta fila!", ephemeral=True)
            return

        # Remove o jogador da fila
        active_queues[self.value].remove(user.id)

        # Atualiza a mensagem da fila
        players_in_queue = active_queues[self.value]
        embed = get_queue_embed(self.value, players_in_queue)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message(f"Você saiu da fila de {self.value} R$!", ephemeral=True)

class QueuesCog(commands.Cog, name="Queues"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra as Views persistentes para cada valor de fila apenas uma vez
        if not hasattr(self.bot, '_queue_views_registered'):
            for value in config.QUEUE_CONFIG.keys():
                self.bot.add_view(QueueView(self.bot, value))
            self.bot._queue_views_registered = True
        print("Queue Cog is ready. Views registered.")

    @commands.command(name="setup_queues")
    @commands.has_role(config.STAFF_ROLE_ID) # Apenas staff pode usar este comando
    async def setup_queues(self, ctx: commands.Context):
        """Cria as mensagens estáticas das filas no canal configurado."""
        channel = self.bot.get_channel(config.QUEUE_CHANNEL_ID)
        if not channel:
            await ctx.send("Canal de filas não encontrado. Verifique o `QUEUE_CHANNEL_ID` no `config.py`.")
            return

        await ctx.send("Configurando as filas...", delete_after=5)
        # Apaga mensagens antigas para evitar duplicatas (com limite menor para evitar rate limit)
        try:
            await channel.purge(limit=50)
        except discord.HTTPException:
            pass  # Ignora erros de rate limit
        
        # Cria uma mensagem para cada fila configurada
        for value in config.QUEUE_CONFIG.keys():
            embed = get_queue_embed(value, [])
            await channel.send(embed=embed, view=QueueView(self.bot, value))

        # Tenta deletar a mensagem do comando, mas ignora se já foi deletada
        try:
            await ctx.message.delete()
        except discord.NotFound:
            pass  # Mensagem já foi deletada, ignora o erro


async def setup(bot: commands.Bot):
    # O `MatchCog` precisa ser carregado antes, pois `QueuesCog` depende dele.
    # O bot principal cuida da ordem de carregamento.
    await bot.add_cog(QueuesCog(bot), guild=discord.Object(id=config.GUILD_ID))