# config.py

BOT_TOKEN = "MTM4NDM0NzA1NTEwMjYyNzg3MA.GRPe1P.iZyDUzcmm2Juei9Aud2f3avpUWJyRm5hBllYiw"  # Cole o token do seu bot aqui
GUILD_ID = 1384260164500062218 # ID do seu servidor (clique direito no servidor > Copiar ID do servidor)

# --- IDs dos Canais e Categorias - REMATCH ---
# Canais de filas tradicionais
QUEUE_2V2_CHANNEL_ID = 1387824474577043456  # Canal #2v2
QUEUE_3V3_CHANNEL_ID = 1387824424618688552  # Canal #3v3
QUEUE_4V4_CHANNEL_ID = 1384362940886679592  # Canal #4v4
QUEUE_5V5_CHANNEL_ID = 1384362985539108894  # Canal #5v5 REMATCH (substitua pelo ID correto se necessário)

MATCH_CATEGORY_ID = 1384362763123425351 # ID da categoria onde os canais de aposta serão criados (#APOSTADO)
STAFF_MEDIATION_CHANNEL_ID = 1384364874225811607 # ID do canal #AGUARDANDO MEDIACAO
LOGS_CHANNEL_ID = 1384269500257407048 # ID do canal #logs-apostas




# --- Valorant ---
VALORANT_QUEUE_CHANNEL_ID = 1384606408011550872  # ID do canal #5v5 (Valorant)
VALORANT_MATCH_CATEGORY_ID = 1384606328655183964  # ID da categoria Valorant

# --- ID do Cargo ---
STAFF_ROLE_ID = 1384260233194373220 # ID do cargo da Staff/Mediador

# --- IDs das Roles de Seleção de Jogo ---
VALORANT_PLAYER_ROLE_ID = 1390042459194527885  # Substitua pelo ID da role @Jogador Valorant
REMATCH_PLAYER_ROLE_ID = 1390042617126846484   # Substitua pelo ID da role @Jogador Rematch

# --- IDs para Sistema de Times ---
TEAM_CATEGORY_ID = 1390058789843108010  # ID da categoria #MEU-TIME
PROCURAR_TIMES_CHANNEL_ID = 1390047079413448735  # ID do canal #procurar-times
RANKING_CHANNEL_ID = 1390072574762946611  # ID do canal #ranking-times
REMATCH_RANKING_CHANNEL_ID = 1390406965125189732  # ID do canal #ranking-rematch

# --- IDs dos Cargos de Ranks REMATCH ---
REMATCH_RANK_ROLES = {
    "Bronze 3": 1390404187627716791,    # ID do cargo @Bronze 3
    "Bronze 2": 1390404137904242930,    # ID do cargo @Bronze 2
    "Bronze 1": 1390403839370461215,    # ID do cargo @Bronze 1
    "Silver 3": 1390404449775783986,    # ID do cargo @Silver 3
    "Silver 2": 1390404409770639370,    # ID do cargo @Silver 2
    "Silver 1": 1390404260147236934,    # ID do cargo @Silver 1
    "Gold 3": 1390404540028944396,      # ID do cargo @Gold 3
    "Gold 2": 1390404620601528429,      # ID do cargo @Gold 2
    "Gold 1": 1390404655795929241,      # ID do cargo @Gold 1
    "Platinum 3": 1390404694081540189,  # ID do cargo @Platinum 3
    "Platinum 2": 1390404886922788864,  # ID do cargo @Platinum 2
    "Platinum 1": 1390404903955988480,  # ID do cargo @Platinum 1
    "Diamond 3": 1390404945198714970,   # ID do cargo @Diamond 3
    "Diamond 2": 1390405005445562458,   # ID do cargo @Diamond 2
    "Diamond 1": 1390405021073674341,   # ID do cargo @Diamond 1
    "Elite 3": 1390405108650741760,     # ID do cargo @Elite 3
    "Elite 2": 1390405275122663454,     # ID do cargo @Elite 2
    "Elite 1": 1390405327324844162,     # ID do cargo @Elite 1
}

# --- IDs dos Cargos de Rank (substitua pelos IDs reais) --- VALORANT
RANK_ROLES = {
    "Iron 1": 1390035099382186085,      # Substitua por ID real do cargo
    "Iron 2": 1390035998280257698,      # Substitua por ID real do cargo
    "Iron 3": 1390035501607555243,      # Substitua por ID real do cargo
    "Bronze 1": 1390036368222322718,    # Substitua por ID real do cargo
    "Bronze 2": 1390037503708368926,    # Substitua por ID real do cargo
    "Bronze 3": 1390036948386578444,    # Substitua por ID real do cargo
    "Silver 1": 1390037651809243308,    # Substitua por ID real do cargo
    "Silver 2": 1390037743647592469,    # Substitua por ID real do cargo
    "Silver 3": 1390038361334353930,    # Substitua por ID real do cargo
    "Gold 1": 1390037660357230622,      # Substitua por ID real do cargo
    "Gold 2": 1390037791676698644,      # Substitua por ID real do cargo
    "Gold 3": 1390037834580103450,      # Substitua por ID real do cargo
    "Platinum 1": 1390037216369180772,  # Substitua por ID real do cargo
    "Platinum 2": 1390037470833545487,  # Substitua por ID real do cargo
    "Platinum 3": 1390037550592299150,  # Substitua por ID real do cargo
    "Diamond 1": 1390036919252947025,   # Substitua por ID real do cargo
    "Diamond 2": 1390037103341080586,   # Substitua por ID real do cargo
    "Diamond 3": 1390037129416937562,   # Substitua por ID real do cargo
    "Ascendant 1": 1390037770717626490, # Substitua por ID real do cargo
    "Ascendant 2": 1390037915731759206, # Substitua por ID real do cargo
    "Ascendant 3": 1390037949571272816, # Substitua por ID real do cargo
    "Immortal 1": 1390036641963311124,  # Substitua por ID real do cargo
    "Immortal 2": 1390036544529895574,  # Substitua por ID real do cargo
    "Immortal 3": 1390036368218128545,  # Substitua por ID real do cargo
    "Radiant": 1390035837055664148,     # Substitua por ID real do cargo
}

# --- Configurações das Filas ---
# Configurações para cada tipo de fila
QUEUE_2V2_CONFIG = {
    5: {"name": "APOSTA 2x2 - 5 R$", "max_players": 2},
    10: {"name": "APOSTA 2x2 - 10 R$", "max_players": 2},
    15: {"name": "APOSTA 2x2 - 15 R$", "max_players": 2},
    20: {"name": "APOSTA 2x2 - 20 R$", "max_players": 2},
    50: {"name": "APOSTA 2x2 - 50 R$", "max_players": 2},
    100: {"name": "APOSTA 2x2 - 100 R$", "max_players": 2},
}

QUEUE_3V3_CONFIG = {
    5: {"name": "APOSTA 3x3 - 5 R$", "max_players": 2},
    10: {"name": "APOSTA 3x3 - 10 R$", "max_players": 2},
    15: {"name": "APOSTA 3x3 - 15 R$", "max_players": 2},
    20: {"name": "APOSTA 3x3 - 20 R$", "max_players": 2},
    50: {"name": "APOSTA 3x3 - 50 R$", "max_players": 2},
    100: {"name": "APOSTA 3x3 - 100 R$", "max_players": 2},
}

QUEUE_4V4_CONFIG = {
    5: {"name": "APOSTA 4x4 - 5 R$", "max_players": 2},
    10: {"name": "APOSTA 4x4 - 10 R$", "max_players": 2},
    15: {"name": "APOSTA 4x4 - 15 R$", "max_players": 2},
    20: {"name": "APOSTA 4x4 - 20 R$", "max_players": 2},
    50: {"name": "APOSTA 4x4 - 50 R$", "max_players": 2},
    100: {"name": "APOSTA 4x4 - 100 R$", "max_players": 2},
}

QUEUE_5V5_CONFIG = {
    5: {"name": "APOSTA 5x5 - 5 R$", "max_players": 2},
    10: {"name": "APOSTA 5x5 - 10 R$", "max_players": 2},
    15: {"name": "APOSTA 5x5 - 15 R$", "max_players": 2},
    20: {"name": "APOSTA 5x5 - 20 R$", "max_players": 2},
    50: {"name": "APOSTA 5x5 - 50 R$", "max_players": 2},
    100: {"name": "APOSTA 5x5 - 100 R$", "max_players": 2},
}

# Compatibilidade com cog antigo (usa 5v5 como padrão)
QUEUE_CONFIG = QUEUE_5V5_CONFIG
QUEUE_CHANNEL_ID = QUEUE_5V5_CHANNEL_ID

# Mapeamento de canais para configurações
QUEUE_CHANNELS = {
    "2v2": {"channel_id": QUEUE_2V2_CHANNEL_ID, "config": QUEUE_2V2_CONFIG},
    "3v3": {"channel_id": QUEUE_3V3_CHANNEL_ID, "config": QUEUE_3V3_CONFIG},
    "4v4": {"channel_id": QUEUE_4V4_CHANNEL_ID, "config": QUEUE_4V4_CONFIG},
    "5v5": {"channel_id": QUEUE_5V5_CHANNEL_ID, "config": QUEUE_5V5_CONFIG},
}

# --- Configurações das Filas Valorant ---
VALORANT_QUEUE_CONFIG = {
    15: {"name": "APOSTA 5x5 - 15 R$ - VALORANT", "max_players": 2},
    20: {"name": "APOSTA 5x5 - 20 R$ - VALORANT", "max_players": 2},
    25: {"name": "APOSTA 5x5 - 25 R$ - VALORANT", "max_players": 2},
    30: {"name": "APOSTA 5x5 - 30 R$ - VALORANT", "max_players": 2},
    50: {"name": "APOSTA 5x5 - 50 R$ - VALORANT", "max_players": 2},
    100: {"name": "APOSTA 5x5 - 100 R$ - VALORANT", "max_players": 2},
}

# Nota: Embora o nome seja 5x5, a lógica está para 2 jogadores conforme o exemplo.
# Para uma partida 5x5 real, mude "max_players" para 10.