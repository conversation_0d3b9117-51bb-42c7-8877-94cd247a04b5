# main.py

import discord
from discord.ext import commands
import config

class BettingBot(commands.Bot):
    def __init__(self):
        # Intents são as permissões que o bot solicita ao Discord
        intents = discord.Intents.default()
        intents.members = True # Necessário para obter dados dos membros (ex: roles)
        intents.message_content = True # Necessário para comandos de prefixo, como !setup_queues
        
        super().__init__(command_prefix="!", intents=intents)

    async def setup_hook(self):
        """Este método é chamado quando o bot está se preparando para conectar."""
        print("Loading cogs...")
        # A ordem aqui é importante se um cog depende do outro.
        # Por agora, não há dependências estritas no carregamento.
        await self.load_extension("cogs.match")
        await self.load_extension("cogs.queues")
        await self.load_extension("cogs.multi_queues")
        await self.load_extension("cogs.valorant")
        await self.load_extension("cogs.ranks_teams")
        await self.load_extension("cogs.team_channels")
        try:
            await self.load_extension("cogs.team_ranking")
            print("Team Ranking Cog loaded successfully")
        except Exception as e:
            print(f"Error loading team_ranking cog: {e}")
        await self.load_extension("cogs.rank_interface")
        await self.load_extension("cogs.rematch_ranks_teams")
        await self.load_extension("cogs.rematch_queues")
        await self.load_extension("cogs.rematch_ranking")
        await self.load_extension("cogs.rematch_interface")
        await self.load_extension("cogs.game_selection")
        await self.load_extension("cogs.staff")
        
        # Sincroniza os comandos de barra (/) com o servidor especificado no config
        # Isso garante que o comando /finalizar apareça para os usuários
        await self.tree.sync(guild=discord.Object(id=config.GUILD_ID))

    async def on_ready(self):
        print(f'Bot conectado como {self.user} (ID: {self.user.id})')
        print('------')

bot = BettingBot()

if __name__ == "__main__":
    if not config.BOT_TOKEN or config.BOT_TOKEN == "SEU_TOKEN_AQUI":
        print("ERRO: O token do bot não foi definido no arquivo config.py")
    else:
        bot.run(config.BOT_TOKEN)