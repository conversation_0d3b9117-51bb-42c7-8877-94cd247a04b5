# cogs/game_selection.py

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import config

# IDs das roles (configuradas no config.py)
VALORANT_ROLE_ID = config.VALORANT_PLAYER_ROLE_ID
REMATCH_ROLE_ID = config.REMATCH_PLAYER_ROLE_ID

class GameSelectionButton(Button):
    def __init__(self, game_type: str, emoji: str, label: str, role_id: int):
        super().__init__(
            style=discord.ButtonStyle.primary if game_type == "valorant" else discord.ButtonStyle.secondary,
            emoji=emoji,
            label=label,
            custom_id=f"game_selection_{game_type}"
        )
        self.game_type = game_type
        self.role_id = role_id

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user
        guild = interaction.guild
        
        if not guild:
            await interaction.response.send_message("❌ Erro: Servidor não encontrado.", ephemeral=True)
            return
        
        role = guild.get_role(self.role_id)
        if not role:
            await interaction.response.send_message(f"❌ Erro: Role {self.game_type} não encontrada.", ephemeral=True)
            return
        
        # Verifica se o usuário já tem a role
        if role in user.roles:
            # Remove a role
            try:
                await user.remove_roles(role, reason=f"Removeu interesse em {self.game_type}")
                
                embed = discord.Embed(
                    title="❌ Jogo Removido",
                    description=f"Você não verá mais os canais de **{self.game_type.upper()}**",
                    color=discord.Color.red()
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
            except discord.Forbidden:
                await interaction.response.send_message("❌ Erro: Bot sem permissão para remover roles.", ephemeral=True)
            except discord.HTTPException:
                await interaction.response.send_message("❌ Erro ao remover role.", ephemeral=True)
        else:
            # Adiciona a role
            try:
                await user.add_roles(role, reason=f"Escolheu jogar {self.game_type}")
                
                # Mensagens personalizadas por jogo
                if self.game_type == "valorant":
                    description = (
                        f"Agora você pode ver os canais de **VALORANT**!\n\n"
                        f"📊 Use `/registrar_rank` para começar\n"
                        f"👥 Use `/criar_time` para formar seu time\n"
                        f"🎯 Entre nas filas quando tiver um time completo"
                    )
                else:  # rematch
                    description = (
                        f"Agora você pode ver os canais de **REMATCH**!\n\n"
                        f"🎮 Entre nas filas e comece a apostar!\n"
                        f"⚽ Temos filas apostadas de 2v2, 3v3, 4v4 e 5v5!\n"
                        f"💬 Use nossos chat  para conversar com outros jogadores e achar um time para você!\n\n"
                        f"🔴 EM BREVE: Sistema de ranks e times!"
                    )
                
                embed = discord.Embed(
                    title="✅ Jogo Selecionado!",
                    description=description,
                    color=discord.Color.green()
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
            except discord.Forbidden:
                await interaction.response.send_message("❌ Erro: Bot sem permissão para adicionar roles.", ephemeral=True)
            except discord.HTTPException:
                await interaction.response.send_message("❌ Erro ao adicionar role.", ephemeral=True)

class GameSelectionView(View):
    def __init__(self):
        super().__init__(timeout=None)
        
        # Botão Valorant
        self.add_item(GameSelectionButton(
            game_type="valorant",
            emoji="🔴",
            label="VALORANT",
            role_id=VALORANT_ROLE_ID
        ))
        
        # Botão Rematch
        self.add_item(GameSelectionButton(
            game_type="rematch", 
            emoji="🔫",
            label="REMATCH",
            role_id=REMATCH_ROLE_ID
        ))

class GameSelectionCog(commands.Cog, name="GameSelection"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra a view persistente
        if not hasattr(self.bot, '_game_selection_view_registered'):
            self.bot.add_view(GameSelectionView())
            self.bot._game_selection_view_registered = True
        print("Game Selection Cog is ready. View registered.")

    @commands.command(name="setup_game_selection")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_game_selection(self, ctx):
        """Cria a mensagem de seleção de jogos no canal atual."""
        
        embed = discord.Embed(
            title="🎮 Bem-vindo ao Servidor!",
            description=(
                "Escolha qual(is) jogo(s) você joga para ver os canais correspondentes:\n\n"
                "🔴 **VALORANT** - Sistema de ranks, times e apostas 5v5\n"
                "🔫 **REMATCH** - Apostas tradicionais 2v2, 3v3, 4v4, 5v5\n\n"
                "💡 **Dica:** Você pode escolher ambos os jogos clicando nos dois botões!\n"
                "🔄 **Para remover:** Clique novamente no botão para remover o acesso."
            ),
            color=discord.Color.blue()
        )
        
        embed.add_field(
            name="📋 Como funciona:",
            value=(
                "• Clique no botão do jogo que você joga\n"
                "• Os canais do jogo aparecerão para você\n"
                "• Clique novamente para remover o acesso"
            ),
            inline=False
        )
        
        embed.set_footer(text="Sistema de Seleção de Jogos • WantedQueue")
        
        view = GameSelectionView()
        await ctx.send(embed=embed, view=view)
        
        # Tenta deletar a mensagem do comando
        try:
            await ctx.message.delete()
        except discord.NotFound:
            pass

    @commands.command(name="meus_jogos")
    async def my_games(self, ctx):
        """Mostra quais jogos você tem acesso."""
        user = ctx.author
        guild = ctx.guild
        
        valorant_role = guild.get_role(VALORANT_ROLE_ID) if VALORANT_ROLE_ID else None
        rematch_role = guild.get_role(REMATCH_ROLE_ID) if REMATCH_ROLE_ID else None
        
        games = []
        if valorant_role and valorant_role in user.roles:
            games.append("🔴 VALORANT")
        if rematch_role and rematch_role in user.roles:
            games.append("🔫 REMATCH")
        
        if games:
            description = f"Você tem acesso aos jogos:\n" + "\n".join(games)
            color = discord.Color.green()
        else:
            description = "Você ainda não escolheu nenhum jogo.\nUse o canal #escolha-seu-jogo para selecionar."
            color = discord.Color.orange()
        
        embed = discord.Embed(
            title="🎮 Seus Jogos",
            description=description,
            color=color
        )
        
        await ctx.send(embed=embed, delete_after=10)
        
        # Tenta deletar a mensagem do comando
        try:
            await ctx.message.delete()
        except discord.NotFound:
            pass

async def setup(bot: commands.Bot):
    await bot.add_cog(GameSelectionCog(bot), guild=discord.Object(id=config.GUILD_ID))
