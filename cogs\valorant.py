# cogs/valorant.py

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import config
from .match import MatchCog # Importamos o Cog de partida para chamá-lo

# Dicionário para armazenar o estado das filas Valorant em memória
# Formato: { 'valor_da_fila': [id_do_jogador1, id_do_jogador2] }
active_valorant_queues = {}

def get_valorant_queue_embed(value: int, players_in_queue: list):
    """Cria o embed da fila Valorant."""
    queue_info = config.VALORANT_QUEUE_CONFIG[value]
    max_players = queue_info["max_players"]
    
    embed = discord.Embed(
        title=f"[{queue_info['name']}]",
        description="Clique no botão abaixo para entrar na fila.",
        color=discord.Color.red()  # Cor vermelha para Valorant
    )
    
    if players_in_queue:
        player_mentions = "\n".join([f"<@{pid}>" for pid in players_in_queue])
        embed.add_field(name="Jogadores na Fila:", value=player_mentions, inline=False)
    else:
        embed.add_field(name="Jogadores na Fila:", value="Nenhum jogador na fila.", inline=False)
    
    embed.add_field(name="Jogadores:", value=f"{len(players_in_queue)}/{max_players}", inline=True)
    
    return embed

class ValorantQueueButton(Button):
    def __init__(self, value: int, label: str):
        super().__init__(label=label, style=discord.ButtonStyle.red, custom_id=f"valorant_queue_button_{value}")
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user
        
        # Verifica se o jogador já está em alguma fila Valorant
        for queue_value, players in active_valorant_queues.items():
            if user.id in players:
                await interaction.response.send_message(f"Você já está na fila de {queue_value} R$ (Valorant)!", ephemeral=True)
                return
        
        # Adiciona o jogador à fila
        if self.value not in active_valorant_queues:
            active_valorant_queues[self.value] = []
        
        active_valorant_queues[self.value].append(user.id)
        players_in_queue = active_valorant_queues[self.value]
        
        # Atualiza a mensagem da fila
        embed = get_valorant_queue_embed(self.value, players_in_queue)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message(f"Você entrou na fila de {self.value} R$ (Valorant)!", ephemeral=True)
        
        # Verifica se a fila está cheia
        max_players = config.VALORANT_QUEUE_CONFIG[self.value]["max_players"]
        if len(players_in_queue) >= max_players:
            # Cria a partida
            match_cog = self.view.bot.get_cog("MatchCog")
            if match_cog:
                await match_cog.create_valorant_match(interaction, players_in_queue, self.value)
                
                # Limpa a fila e atualiza a mensagem original
                active_valorant_queues[self.value].clear()
                new_embed = get_valorant_queue_embed(self.value, [])
                await interaction.message.edit(embed=new_embed)

class ValorantLeaveQueueButton(Button):
    def __init__(self, value: int, label: str):
        super().__init__(label=label, style=discord.ButtonStyle.gray, custom_id=f"valorant_leave_queue_button_{value}")
        self.value = value

    async def callback(self, interaction: discord.Interaction):
        user = interaction.user
        
        # Verifica se o jogador está na fila desta aposta Valorant
        if user.id not in active_valorant_queues[self.value]:
            await interaction.response.send_message("Você não está nesta fila Valorant!", ephemeral=True)
            return
        
        # Remove o jogador da fila
        active_valorant_queues[self.value].remove(user.id)
        
        # Atualiza a mensagem da fila
        players_in_queue = active_valorant_queues[self.value]
        embed = get_valorant_queue_embed(self.value, players_in_queue)
        await interaction.message.edit(embed=embed)
        await interaction.response.send_message(f"Você saiu da fila de {self.value} R$ (Valorant)!", ephemeral=True)

class ValorantQueueView(View):
    def __init__(self, bot: commands.Bot, queue_value: int):
        super().__init__(timeout=None)
        self.bot = bot
        self.queue_value = queue_value
        
        # Cria os botões para entrar e sair da fila Valorant
        self.add_item(ValorantQueueButton(value=queue_value, label=f"Entrar na fila (R${queue_value})"))
        self.add_item(ValorantLeaveQueueButton(value=queue_value, label="Sair da fila"))

class ValorantCog(commands.Cog, name="ValorantCog"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        
        # Inicializa as filas Valorant
        for value in config.VALORANT_QUEUE_CONFIG.keys():
            active_valorant_queues[value] = []

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra as Views persistentes para cada valor de fila Valorant apenas uma vez
        if not hasattr(self.bot, '_valorant_queue_views_registered'):
            for value in config.VALORANT_QUEUE_CONFIG.keys():
                self.bot.add_view(ValorantQueueView(self.bot, value))
            self.bot._valorant_queue_views_registered = True
        print("Valorant Cog is ready. Views registered.")

    @commands.command(name="setup_valorant_queues")
    @commands.has_role(config.STAFF_ROLE_ID)
    async def setup_valorant_queues(self, ctx):
        """Comando para configurar as filas Valorant no canal especificado."""
        channel = self.bot.get_channel(config.VALORANT_QUEUE_CHANNEL_ID)
        
        if not channel:
            await ctx.send("Canal de filas Valorant não encontrado. Verifique o VALORANT_QUEUE_CHANNEL_ID no config.py")
            return

        await ctx.send("Configurando as filas Valorant...", delete_after=5)
        # Limpa mensagens antigas (com limite menor para evitar rate limit)
        try:
            await channel.purge(limit=50)
        except discord.HTTPException:
            pass  # Ignora erros de rate limit

        # Cria uma mensagem para cada fila Valorant configurada
        for value in config.VALORANT_QUEUE_CONFIG.keys():
            embed = get_valorant_queue_embed(value, [])
            await channel.send(embed=embed, view=ValorantQueueView(self.bot, value))

        # Tenta deletar a mensagem do comando, mas ignora se já foi deletada
        try:
            await ctx.message.delete()
        except discord.NotFound:
            pass  # Mensagem já foi deletada, ignora o erro

async def setup(bot: commands.Bot):
    await bot.add_cog(ValorantCog(bot), guild=discord.Object(id=config.GUILD_ID))
