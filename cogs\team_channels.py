# cogs/team_channels.py

import discord
from discord.ext import commands
from discord.ui import Modal, TextInput, Button, View
import config
import json
import os

# Configurações (do config.py)
TEAM_CATEGORY_ID = config.TEAM_CATEGORY_ID
PROCURAR_TIMES_CHANNEL_ID = config.PROCURAR_TIMES_CHANNEL_ID

# Arquivo para armazenar dados dos canais de times
TEAM_CHANNELS_FILE = "team_channels_data.json"

def load_team_channels_data():
    if os.path.exists(TEAM_CHANNELS_FILE):
        with open(TEAM_CHANNELS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_team_channels_data(data):
    with open(TEAM_CHANNELS_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

class PublicarVagaModal(Modal):
    def __init__(self, team_name: str, team_id: str):
        super().__init__(title=f"Publicar Vaga - {team_name}")
        self.team_name = team_name
        self.team_id = team_id
        
        # Campo para rank desejado
        self.rank_input = TextInput(
            label="Rank Desejado",
            placeholder="Ex: Gold 2+, Platinum 1-3, qualquer rank...",
            max_length=50,
            required=True
        )
        self.add_item(self.rank_input)
        
        # Campo para role/posição
        self.role_input = TextInput(
            label="Role/Posição",
            placeholder="Ex: Duelist, Controller, Sentinel, IGL, qualquer...",
            max_length=50,
            required=True
        )
        self.add_item(self.role_input)
        
        # Campo para disponibilidade
        self.disponibilidade_input = TextInput(
            label="Disponibilidade",
            placeholder="Ex: Noites, fins de semana, qualquer horário...",
            max_length=100,
            required=True
        )
        self.add_item(self.disponibilidade_input)
        
        # Campo para observações extras
        self.obs_input = TextInput(
            label="Observações (Opcional)",
            placeholder="Informações extras sobre o que procuram...",
            max_length=200,
            required=False,
            style=discord.TextStyle.paragraph
        )
        self.add_item(self.obs_input)

    async def on_submit(self, interaction: discord.Interaction):
        # Carrega dados do time
        from cogs.ranks_teams import load_data, get_team_data
        data = load_data()
        team_data = get_team_data(self.team_id)
        
        if not team_data:
            await interaction.response.send_message("❌ Time não encontrado.", ephemeral=True)
            return
        
        # Cria embed para publicar
        embed = discord.Embed(
            title=f"🔍 {self.team_name} - Procura Jogador!",
            color=discord.Color.gold()
        )
        
        embed.add_field(name="🎖️ Rank Desejado", value=self.rank_input.value, inline=True)
        embed.add_field(name="🎯 Role/Posição", value=self.role_input.value, inline=True)
        embed.add_field(name="⏰ Disponibilidade", value=self.disponibilidade_input.value, inline=True)
        
        if self.obs_input.value:
            embed.add_field(name="📝 Observações", value=self.obs_input.value, inline=False)
        
        # Info do time atual
        current_members = len(team_data["members"])
        avg_rank = team_data.get("average_rank_name", "Calculando...")
        
        embed.add_field(
            name="📊 Info do Time",
            value=f"**Membros:** {current_members}/5\n**Rank Médio:** {avg_rank}",
            inline=True
        )
        
        embed.add_field(
            name="📞 Como Entrar",
            value="Reaja com ✅ para demonstrar interesse!",
            inline=True
        )
        
        embed.set_footer(text=f"Publicado por {interaction.user.display_name}")
        
        # Publica no canal #procurar-times
        guild = interaction.guild
        procurar_channel = guild.get_channel(PROCURAR_TIMES_CHANNEL_ID)
        
        if procurar_channel:
            try:
                message = await procurar_channel.send(embed=embed)
                await message.add_reaction("✅")
                
                await interaction.response.send_message(
                    f"✅ Vaga publicada com sucesso em {procurar_channel.mention}!",
                    ephemeral=True
                )
            except discord.Forbidden:
                await interaction.response.send_message(
                    "❌ Não tenho permissão para enviar mensagens no canal de procurar times.",
                    ephemeral=True
                )
        else:
            await interaction.response.send_message(
                "❌ Canal #procurar-times não encontrado.",
                ephemeral=True
            )

class TeamChannelsCog(commands.Cog, name="TeamChannels"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    async def create_team_channels(self, guild, team_name: str, team_id: str, captain_id: int):
        """Cria os canais e cargo para um time."""
        
        # Cria o cargo do time
        try:
            team_role = await guild.create_role(
                name=team_name,
                color=discord.Color.blue(),
                reason=f"Cargo do time {team_name}"
            )
        except discord.Forbidden:
            return False, "Sem permissão para criar cargos"
        except discord.HTTPException:
            return False, "Erro ao criar cargo do time"
        
        # Pega a categoria MEU-TIME
        category = guild.get_channel(TEAM_CATEGORY_ID)
        if not category:
            return False, "Categoria MEU-TIME não encontrada"
        
        # Permissões para os canais
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(read_messages=False),
            team_role: discord.PermissionOverwrite(read_messages=True, send_messages=True),
            guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True, manage_channels=True)
        }
        
        # Permissões para canal de voz
        voice_overwrites = {
            guild.default_role: discord.PermissionOverwrite(view_channel=False),
            team_role: discord.PermissionOverwrite(view_channel=True, connect=True, speak=True),
            guild.me: discord.PermissionOverwrite(view_channel=True, manage_channels=True)
        }
        
        try:
            # Cria canal de gerenciamento
            manage_channel = await guild.create_text_channel(
                name=f"{team_name.lower().replace(' ', '-')}-gerenciar",
                category=category,
                overwrites=overwrites,
                topic=f"Gerenciamento do time {team_name}"
            )
            
            # Cria canal de chat
            chat_channel = await guild.create_text_channel(
                name=f"{team_name.lower().replace(' ', '-')}-chat",
                category=category,
                overwrites=overwrites,
                topic=f"Chat do time {team_name}"
            )
            
            # Cria canal de voz
            voice_channel = await guild.create_voice_channel(
                name=team_name,
                category=category,
                overwrites=voice_overwrites
            )
            
            # Salva dados dos canais
            team_channels_data = load_team_channels_data()
            team_channels_data[team_id] = {
                "team_name": team_name,
                "role_id": team_role.id,
                "manage_channel_id": manage_channel.id,
                "chat_channel_id": chat_channel.id,
                "voice_channel_id": voice_channel.id,
                "captain_id": captain_id
            }
            save_team_channels_data(team_channels_data)
            
            # Cria mensagem fixa no canal de gerenciamento
            await self.create_management_message(manage_channel, team_name)
            
            return True, {
                "role": team_role,
                "manage_channel": manage_channel,
                "chat_channel": chat_channel,
                "voice_channel": voice_channel
            }
            
        except discord.Forbidden:
            return False, "Sem permissão para criar canais"
        except discord.HTTPException:
            return False, "Erro ao criar canais"

    async def create_management_message(self, channel, team_name: str):
        """Cria a mensagem fixa de gerenciamento no canal do time."""
        
        embed = discord.Embed(
            title=f"🏆 Gerenciamento - {team_name}",
            description="Central de comandos para gerenciar seu time",
            color=discord.Color.blue()
        )
        
        embed.add_field(
            name="👥 Comandos de Membros",
            value=(
                "`/convidar @jogador` - Convida um jogador\n"
                "`/remover @jogador` - Remove um jogador\n"
                "`/promover @jogador` - Torna capitão\n"
                "`/meu_time` - Informações do time"
            ),
            inline=False
        )
        
        embed.add_field(
            name="⚙️ Comandos de Gerenciamento",
            value=(
                "`/recalcular_rank` - Recalcula rank médio\n"
                "`/dissolver_time` - Dissolve o time\n"
                "`/publicar_vaga` - Publica vaga no #procurar-times"
            ),
            inline=False
        )
        
        embed.add_field(
            name="📋 Como Publicar Vaga",
            value=(
                "Use `/publicar_vaga` para abrir um formulário onde você pode:\n"
                "• Especificar rank desejado\n"
                "• Definir role/posição\n"
                "• Informar disponibilidade\n"
                "• Adicionar observações extras"
            ),
            inline=False
        )
        
        embed.set_footer(text="Esta mensagem é fixa - use os comandos acima para gerenciar seu time")
        
        try:
            message = await channel.send(embed=embed)
            await message.pin()
        except discord.Forbidden:
            pass  # Se não conseguir fixar, ignora

    @commands.command(name="publicar_vaga")
    async def publicar_vaga(self, ctx):
        """Abre modal para publicar vaga do time."""
        
        # Verifica se está em um canal de gerenciamento de time
        team_channels_data = load_team_channels_data()
        
        team_found = None
        for team_id, team_info in team_channels_data.items():
            if team_info["manage_channel_id"] == ctx.channel.id:
                team_found = (team_id, team_info)
                break
        
        if not team_found:
            await ctx.send("❌ Este comando só pode ser usado no canal de gerenciamento do seu time.", delete_after=5)
            return
        
        team_id, team_info = team_found
        
        # Verifica se é o capitão
        from cogs.ranks_teams import get_team_data
        team_data = get_team_data(team_id)
        
        if not team_data or team_data["captain"] != str(ctx.author.id):
            await ctx.send("❌ Apenas o capitão pode publicar vagas.", delete_after=5)
            return
        
        # Abre o modal
        modal = PublicarVagaModal(team_info["team_name"], team_id)
        await ctx.send("📝 Abrindo formulário para publicar vaga...", delete_after=3)
        
        # Como não podemos enviar modal via comando normal, vamos criar uma view com botão
        view = PublicarVagaView(team_info["team_name"], team_id)
        await ctx.send("Clique no botão abaixo para abrir o formulário:", view=view, delete_after=30)

class PublicarVagaButton(Button):
    def __init__(self, team_name: str, team_id: str):
        super().__init__(label="📝 Publicar Vaga", style=discord.ButtonStyle.primary)
        self.team_name = team_name
        self.team_id = team_id

    async def callback(self, interaction: discord.Interaction):
        modal = PublicarVagaModal(self.team_name, self.team_id)
        await interaction.response.send_modal(modal)

class PublicarVagaView(View):
    def __init__(self, team_name: str, team_id: str):
        super().__init__(timeout=30)
        self.add_item(PublicarVagaButton(team_name, team_id))

async def setup(bot: commands.Bot):
    await bot.add_cog(TeamChannelsCog(bot), guild=discord.Object(id=config.GUILD_ID))
