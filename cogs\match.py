# cogs/match.py

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import config


# Dicionário para rastrear partidas ativas e seus dados
# { channel_id: { "players": [...], "value": 50, "mediator": None } }
active_matches = {}

class MediationView(View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Aceitar Mediação", style=discord.ButtonStyle.primary, custom_id="accept_mediation_button")
    async def accept_mediation(self, interaction: discord.Interaction, button: Button):
        mediator = interaction.user
        channel = interaction.channel # Canal #aguardando-mediacao
        
        # Pega o ID do canal da partida do embed
        match_channel_id = int(interaction.message.embeds[0].footer.text.split(": ")[1])
        match_channel = interaction.guild.get_channel(match_channel_id)

        if not match_channel:
            await interaction.response.send_message("Canal da partida não encontrado. Pode já ter sido finalizado.", ephemeral=True)
            return

        # Adiciona o mediador ao canal da partida
        await match_channel.set_permissions(mediator, read_messages=True, send_messages=True)
        
        # Renomeia o canal da partida
        await match_channel.edit(name=f"aposta-{match_channel.id}-mediador-{mediator.name}")

        # Atualiza o estado da partida
        if match_channel_id in active_matches:
            active_matches[match_channel_id]["mediator"] = mediator.id

        # Envia notificação no canal da aposta
        await match_channel.send(f"**Mediador:** {mediator.mention} entrou para mediar a aposta.\nEle cuidará do recebimento das ambas as partes, e verificará o ganhador, e passará o dinheiro do mesmo.")

        # Atualiza a mensagem de mediação para indicar que foi aceita
        original_embed = interaction.message.embeds[0]
        new_embed = discord.Embed.from_dict(original_embed.to_dict())
        new_embed.title = f"✅ MEDIAÇÃO ACEITA POR {mediator.name}"
        new_embed.color = discord.Color.green()

        await interaction.message.edit(embed=new_embed, view=None) # Remove o botão
        await interaction.response.send_message(f"Você aceitou mediar a aposta no canal {match_channel.mention}!", ephemeral=True)


class MatchCog(commands.Cog, name="MatchCog"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra a view de mediação para ser persistente apenas uma vez
        if not hasattr(self.bot, '_mediation_view_registered'):
            self.bot.add_view(MediationView())
            self.bot._mediation_view_registered = True
        print("Match Cog is ready. View registered.")

    async def create_match(self, interaction: discord.Interaction, players: list, value: int):
        guild = interaction.guild
        queue_info = config.QUEUE_CONFIG[value]
        
        # Permissões: Ninguém vê o canal, exceto os jogadores da aposta
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(read_messages=False),
            # Adiciona os jogadores ao canal
            **{guild.get_member(player_id): discord.PermissionOverwrite(read_messages=True, send_messages=True) for player_id in players},
             # Garante que o bot possa gerenciar o canal
            guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True, manage_channels=True)
        }

        # Cria o canal da partida
        category = guild.get_channel(config.MATCH_CATEGORY_ID)
        match_channel = await guild.create_text_channel(
            name=f"aguardando-mediador",
            category=category,
            overwrites=overwrites
        )
        
        # Renomeia o canal com seu próprio ID para garantir unicidade
        await match_channel.edit(name=f"aposta-{match_channel.id}-aguardando")

        # Registra a partida com jogadores originais
        active_matches[match_channel.id] = {
            "players": players,
            "original_players": players.copy(),  # Cópia dos jogadores originais
            "value": value,
            "mediator": None
        }
        
        # Notifica o canal de mediação da staff
        mediation_channel = guild.get_channel(config.STAFF_MEDIATION_CHANNEL_ID)
        player_mentions = " ".join([f"<@{pid}>" for pid in players])

        embed = discord.Embed(
            title=f"🚨 NOVA MEDIAÇÃO - {queue_info['name']} 🚨",
            description=f"Uma nova aposta precisa de um mediador.\n\n**Jogadores:**\n{player_mentions}",
            color=discord.Color.orange()
        )
        embed.set_footer(text=f"ID do Canal da Partida: {match_channel.id}")
        
        await mediation_channel.send(embed=embed, view=MediationView())

        # Envia uma mensagem inicial no canal da partida
        await match_channel.send(f"Canal da aposta criado! {player_mentions}\n\nAguardando um mediador aceitar a partida...")

    async def create_valorant_match(self, interaction: discord.Interaction, players: list, value: int):
        guild = interaction.guild
        queue_info = config.VALORANT_QUEUE_CONFIG[value]

        # Permissões: Ninguém vê o canal, exceto os jogadores da aposta
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(read_messages=False),
            # Adiciona os jogadores ao canal
            **{guild.get_member(player_id): discord.PermissionOverwrite(read_messages=True, send_messages=True) for player_id in players},
             # Garante que o bot possa gerenciar o canal
            guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True, manage_channels=True)
        }

        # Cria o canal da partida na categoria Valorant
        category = guild.get_channel(config.VALORANT_MATCH_CATEGORY_ID)
        match_channel = await guild.create_text_channel(
            name=f"aguardando-mediador-valorant",
            category=category,
            overwrites=overwrites
        )

        # Renomeia o canal com seu próprio ID para garantir unicidade
        await match_channel.edit(name=f"aposta-valorant-{match_channel.id}-aguardando")

        # Registra a partida com jogadores originais
        active_matches[match_channel.id] = {
            "players": players,
            "original_players": players.copy(),  # Cópia dos jogadores originais
            "value": value,
            "mediator": None,
            "game_type": "VALORANT"  # Identifica como aposta Valorant
        }

        # Notifica o canal de mediação da staff
        mediation_channel = guild.get_channel(config.STAFF_MEDIATION_CHANNEL_ID)
        player_mentions = " ".join([f"<@{pid}>" for pid in players])

        embed = discord.Embed(
            title=f"🚨 NOVA MEDIAÇÃO - {queue_info['name']} 🚨",
            description=f"Uma nova aposta Valorant precisa de um mediador.\n\n**Jogadores:**\n{player_mentions}",
            color=discord.Color.red()  # Cor vermelha para Valorant
        )
        embed.set_footer(text=f"ID do Canal da Partida: {match_channel.id}")

        await mediation_channel.send(embed=embed, view=MediationView())

        # Envia uma mensagem inicial no canal da partida
        await match_channel.send(f"Canal da aposta Valorant criado! {player_mentions}\n\nAguardando um mediador aceitar a partida...")

async def setup(bot: commands.Bot):
    await bot.add_cog(MatchCog(bot), guild=discord.Object(id=config.GUILD_ID))