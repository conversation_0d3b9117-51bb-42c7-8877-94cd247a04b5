# cogs/staff.py

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Modal, TextInput
import config as config
import re
from datetime import datetime
from .match import active_matches # Importa o dicionário para limpar a partida
from discord.ui import <PERSON><PERSON>, View

class FinalizeModal(Modal):
    def __init__(self, bot: commands.Bot, match_data: dict, channel_id: int):
        # Cria um título único para evitar cache
        import time
        unique_title = f"Finalizar Aposta - {int(time.time())}"
        super().__init__(title=unique_title)

        self.bot = bot
        self.match_data = match_data
        self.channel_id = channel_id

        # Sempre cria campos novos para evitar reutilização
        self.winner = TextInput(
            label="Vencedor da Aposta",
            placeholder="Digite @ seguido do nome do usuário (ex: @usuario) ou cole o ID do usuário.",
            required=True,
            custom_id=f"winner_{int(time.time())}"  # ID único
        )
        self.add_item(self.winner)

        # Se o valor for desconhecido, adiciona campo para o mediador informar
        if match_data.get("value") == "Desconhecido":
            self.bet_value = TextInput(
                label="Valor da Aposta (R$)",
                placeholder="Digite o valor da aposta (apenas números, ex: 50)",
                required=True,
                custom_id=f"bet_value_{int(time.time())}"  # ID único
            )
            self.add_item(self.bet_value)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            winner_input = self.winner.value.strip()

            # Pega os dados da partida
            players = self.match_data.get("players", [])
            value = self.match_data.get("value", "N/A")
            mediator_id = interaction.user.id

            print(f"DEBUG: Modal submetido - Jogadores: {players}, Valor: {value}")

            # Se o valor era desconhecido, pega do formulário
            if value == "Desconhecido" and hasattr(self, 'bet_value'):
                try:
                    value = int(self.bet_value.value.strip())
                    print(f"DEBUG: Valor atualizado do formulário: {value}")
                except ValueError:
                    await interaction.response.send_message("❌ Valor da aposta inválido. Digite apenas números.", ephemeral=True)
                    return
        except Exception as e:
            print(f"DEBUG: Erro no início do on_submit: {e}")
            await interaction.response.send_message("❌ Erro interno. Tente novamente.", ephemeral=True)
            return

        # Debug: Mostra informações sobre os jogadores
        players_debug = ", ".join([str(p) for p in players])

        # Tenta extrair o ID do usuário da menção ou usar como ID direto
        winner_id = None

        # Verifica se é uma menção (formato <@123456789> ou <@!123456789>)
        mention_match = re.match(r'<@!?(\d+)>', winner_input)
        if mention_match:
            winner_id = int(mention_match.group(1))
        else:
            # Tenta converter diretamente para int (caso seja apenas o ID)
            try:
                winner_id = int(winner_input)
            except ValueError:
                # Se não conseguir converter para int, tenta buscar por nome de usuário
                # Busca o usuário pelo nome no servidor
                guild = interaction.guild
                found_user = None

                # Remove @ se existir no início
                username = winner_input.lstrip('@')

                # Busca por nome de usuário ou display name
                for member in guild.members:
                    if (member.name.lower() == username.lower() or
                        member.display_name.lower() == username.lower() or
                        str(member) == winner_input):
                        found_user = member
                        break

                if found_user:
                    winner_id = found_user.id
                else:
                    await interaction.response.send_message(
                        f"❌ **Formato inválido ou usuário não encontrado.**\n\n"
                        f"**Entrada:** `{winner_input}`\n"
                        f"**Jogadores na partida:** {players_debug}\n\n"
                        f"**Formatos aceitos:**\n"
                        f"• Menção: `<@123456789>`\n"
                        f"• ID: `123456789`\n"
                        f"• Nome: `@usuario` ou `usuario`",
                        ephemeral=True
                    )
                    return

        # Validação: verifica se o winner_id está na lista de jogadores originais
        original_players = self.match_data.get("original_players", players)
        original_players_debug = ", ".join([str(p) for p in original_players])

        if winner_id not in original_players:
            await interaction.response.send_message(
                f"❌ **Usuário não encontrado na partida.**\n\n"
                f"**ID encontrado:** `{winner_id}`\n"
                f"**Jogadores originais:** {original_players_debug}\n"
                f"**Todos no canal:** {players_debug}\n\n"
                f"Certifique-se de que o usuário é um dos jogadores originais da aposta.",
                ephemeral=True
            )
            return

        # Monta o log usando apenas os jogadores originais
        log_channel = self.bot.get_channel(config.LOGS_CHANNEL_ID)

        # Usa jogadores originais se disponível, senão usa a lista atual
        original_players = self.match_data.get("original_players", players)
        player_mentions = " vs ".join([f"<@{pid}>" for pid in original_players])

        print(f"DEBUG: Jogadores para log - Original: {original_players}, Atual: {players}")

        # Pega a data e hora atual
        now = datetime.now()
        date_time_str = now.strftime("%d/%m/%Y às %H:%M:%S")

        # Verifica se é uma aposta Valorant
        game_type = self.match_data.get("game_type", "")
        title = "✅ Aposta Finalizada"
        if game_type == "VALORANT":
            title = "✅ Aposta Valorant Finalizada"

        log_embed = discord.Embed(
            title=title,
            color=discord.Color.red() if game_type == "VALORANT" else discord.Color.dark_green(),
            timestamp=discord.utils.utcnow()
        )
        log_embed.add_field(name="💰 Valor", value=f"{value} R$", inline=True)
        log_embed.add_field(name="🏆 Vencedor", value=f"<@{winner_id}>", inline=True)
        log_embed.add_field(name="⚖️ Mediador", value=f"<@{mediator_id}>", inline=True)
        log_embed.add_field(name="👥 Jogadores", value=player_mentions, inline=False)
        log_embed.add_field(name="📅 Finalizada em", value=date_time_str, inline=False)
        log_embed.set_footer(text=f"ID da Aposta (Canal): {self.channel_id}")

        await log_channel.send(embed=log_embed)

        # Integração com sistema de ranking de times
        print(f"🔍 STAFF: Iniciando integração com ranking...")
        print(f"🔍 STAFF: Jogadores originais: {original_players}")
        print(f"🔍 STAFF: Valor da aposta: {value}")

        try:
            from cogs.team_ranking import process_match_result, register_team_victory, update_ranking_channel

            # Processa resultado para detectar times
            result = await process_match_result(self.bot, original_players, value)

            if result.get("teams_detected") and len(result.get("team_ids", [])) == 2:
                # Determina qual time ganhou baseado no vencedor
                winner_team_id = None
                loser_team_id = None

                for team_id, team_info in result["teams"].items():
                    if winner_id in team_info["players"]:
                        winner_team_id = team_id
                    else:
                        loser_team_id = team_id

                if winner_team_id and loser_team_id:
                    # Registra vitória/derrota
                    ranking_result = register_team_victory(winner_team_id, loser_team_id, value)

                    if ranking_result.get("success"):
                        print(f"🏆 RANKING: {ranking_result['winner_name']} venceu {ranking_result['loser_name']}")

                        # Atualiza canal de ranking
                        await update_ranking_channel(self.bot)

                        # Edita o embed do log para incluir info do ranking
                        log_embed.add_field(
                            name="🏆 Ranking de Times",
                            value=f"{ranking_result['winner_name']} +3 pts\n{ranking_result['loser_name']} +0 pts",
                            inline=False
                        )

                        # Reenvia o log atualizado
                        await log_channel.send("📊 **Ranking atualizado automaticamente!**")
                    else:
                        print(f"❌ RANKING: Erro ao registrar vitória: {ranking_result.get('error')}")
                else:
                    print(f"⚠️ RANKING: Não foi possível determinar times vencedor/perdedor")
            else:
                print(f"ℹ️ RANKING: Partida não envolve 2 times registrados ({result.get('teams_count', 0)} times detectados)")

        except Exception as e:
            print(f"❌ RANKING: Erro na integração: {e}")

        # Remove a partida do rastreamento ANTES de responder
        if self.channel_id in active_matches:
            del active_matches[self.channel_id]
            print(f"DEBUG: Removido canal {self.channel_id} do active_matches")

        # Responde à interação antes de deletar o canal
        await interaction.response.send_message("✅ Aposta finalizada com sucesso! O canal será deletado em alguns segundos.", ephemeral=True)

        # Aguarda um pouco antes de deletar para o usuário ver a confirmação
        import asyncio
        await asyncio.sleep(3)

        # Deleta o canal da partida
        await interaction.channel.delete(reason="Aposta finalizada pelo mediador.")

def recover_match_from_channel(channel):
    """Tenta recuperar dados da partida baseado no canal."""
    # Extrai informações do nome do canal
    # Formato esperado: aposta-{channel_id}-aguardando ou aposta-{channel_id}-mediador-{nome}
    if not channel.name.startswith("aposta-"):
        return None

    # Pega os membros do canal (excluindo bots)
    players = []

    for member in channel.members:
        # Exclui apenas bots
        if not member.bot:
            players.append(member.id)

    # Se não encontrou jogadores, tenta uma abordagem alternativa
    if not players:
        # Pega todos os membros que têm permissão específica de ler o canal
        for member in channel.guild.members:
            if channel.permissions_for(member).read_messages and not member.bot:
                # Verifica se o membro tem permissões específicas no canal
                overwrites = channel.overwrites_for(member)
                if overwrites.read_messages is True:
                    players.append(member.id)

    # Filtra apenas jogadores (exclui mediadores que podem ter sido adicionados depois)
    # Pega apenas os primeiros membros não-bot (que são os jogadores originais)
    original_players = []
    for member in channel.members:
        if not member.bot:
            # Verifica se não é um mediador (mediadores geralmente têm permissão de gerenciar canal)
            if not member.guild_permissions.manage_channels:
                original_players.append(member.id)

    # Se não conseguiu filtrar, usa todos os não-bots
    if not original_players:
        original_players = players.copy()

    # Tenta determinar o valor da aposta baseado no histórico de mensagens
    # Por enquanto, vamos usar um valor padrão e deixar o mediador confirmar
    return {
        "players": players,
        "original_players": original_players,  # Jogadores que realmente apostaram
        "value": "Desconhecido",
        "mediator": None
    }

class FinalizeButton(Button):
    def __init__(self, bot, match_data, channel_id):
        super().__init__(label="📝 Finalizar Aposta", style=discord.ButtonStyle.primary)
        self.bot = bot
        self.match_data = match_data
        self.channel_id = channel_id

    async def callback(self, interaction: discord.Interaction):
        modal = FinalizeModal(self.bot, self.match_data, self.channel_id)
        await interaction.response.send_modal(modal)

class FinalizeView(View):
    def __init__(self, bot, match_data, channel_id):
        super().__init__(timeout=300)  # 5 minutos
        self.add_item(FinalizeButton(bot, match_data, channel_id))

class StaffCog(commands.Cog, name="Staff"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="finalizar", description="Finaliza a aposta atual e gera o log.")
    @app_commands.checks.has_role(config.STAFF_ROLE_ID)
    async def finalize(self, interaction: discord.Interaction):
        """Abre o formulário para finalizar uma aposta."""
        # Defer a resposta para dar mais tempo
        await interaction.response.defer(ephemeral=True)

        channel = interaction.channel

        # Verifica se o comando foi usado em um canal de aposta válido
        if not channel.name.startswith("aposta-"):
            await interaction.followup.send("❌ Este comando só pode ser usado em um canal de aposta.", ephemeral=True)
            return

        # Sempre tenta recuperar dados frescos do canal para evitar dados antigos
        recovered_data = recover_match_from_channel(channel)

        if recovered_data and recovered_data["players"]:
            # Usa dados recuperados (sempre frescos)
            match_data = recovered_data
            # Atualiza o active_matches com dados frescos
            active_matches[channel.id] = recovered_data
            print(f"DEBUG: Usando dados recuperados: {match_data}")
        elif channel.id in active_matches:
            # Fallback para dados em memória, mas verifica se são válidos
            stored_data = active_matches[channel.id]
            if stored_data.get("players"):
                match_data = stored_data
                print(f"DEBUG: Usando dados armazenados: {match_data}")
            else:
                await interaction.followup.send(
                    f"❌ **Dados da aposta inválidos.**\n\n"
                    f"**Canal atual:** `{channel.name}` (ID: {channel.id})\n"
                    f"**Problema:** Lista de jogadores vazia\n\n"
                    f"Certifique-se de que os jogadores ainda estão no canal.",
                    ephemeral=True
                )
                return
        else:
            await interaction.followup.send(
                f"❌ **Não foi possível recuperar os dados da aposta.**\n\n"
                f"**Canal atual:** `{channel.name}` (ID: {channel.id})\n"
                f"**Membros encontrados:** {len(recovered_data['players']) if recovered_data else 0}\n\n"
                f"Certifique-se de que os jogadores ainda estão no canal.",
                ephemeral=True
            )
            return

        # Força a recriação do modal para evitar cache
        print(f"DEBUG: Criando modal para canal {channel.id} com dados: {match_data}")

        # Cria view com botão para abrir modal (workaround para defer)
        view = FinalizeView(self.bot, match_data, channel.id)
        await interaction.followup.send("📝 Clique no botão para finalizar a aposta:", view=view, ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(StaffCog(bot), guild=discord.Object(id=config.GUILD_ID))