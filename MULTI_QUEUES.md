# 🎮 Sistema de Filas Múltiplas

## ✅ Implementado com sucesso!

Agora você tem suporte para **4 tipos diferentes de filas**:
- **2v2** - Cor azul
- **3v3** - Cor verde  
- **4v4** - Co<PERSON> laranja
- **5v5** - Cor roxa

## 🔧 Configuração Necessária

### 1. **Configure os IDs dos canais no config.py:**

```python
# Canais de filas (configure com os IDs corretos)
QUEUE_2V2_CHANNEL_ID = seu_canal_2v2_id
QUEUE_3V3_CHANNEL_ID = seu_canal_3v3_id  
QUEUE_4V4_CHANNEL_ID = seu_canal_4v4_id
QUEUE_5V5_CHANNEL_ID = seu_canal_5v5_id
```

### 2. **Estrutura de canais recomendada:**

```
📁 JOGUE AQUI
  ├── 💬 2v2
  ├── 💬 3v3
  ├── 💬 4v4
  └── 💬 5v5

📁 VALORANT
  └── 💬 5v5

📁 APOSTADO (categoria para todas as apostas)
📁 VALORANT APOSTAS (categoria Valorant)
```

## 📝 Comandos Disponíveis

### Para Staff (com cargo configurado):

```bash
# Filas tradicionais
!setup_2v2          # Configura filas no canal 2v2
!setup_3v3          # Configura filas no canal 3v3
!setup_4v4          # Configura filas no canal 4v4
!setup_5v5          # Configura filas no canal 5v5

# Filas Valorant
!setup_valorant_queues  # Configura filas Valorant

# Comandos antigos (ainda funcionam)
!setup_queues       # Configura filas 5v5 (compatibilidade)

# Finalização
/finalizar          # Finaliza apostas (qualquer canal)
```

## 🎯 Como Usar

### 1. **Configure cada canal separadamente:**
```
!setup_2v2    # No Discord, cria filas no canal #2v2
!setup_3v3    # No Discord, cria filas no canal #3v3
!setup_4v4    # No Discord, cria filas no canal #4v4
!setup_5v5    # No Discord, cria filas no canal #5v5
```

### 2. **Cada canal terá suas próprias filas:**
- **2v2**: APOSTA 2x2 - 5 R$, 10 R$, 15 R$, 20 R$, 50 R$, 100 R$
- **3v3**: APOSTA 3x3 - 5 R$, 10 R$, 15 R$, 20 R$, 50 R$, 100 R$
- **4v4**: APOSTA 4x4 - 5 R$, 10 R$, 15 R$, 20 R$, 50 R$, 100 R$
- **5v5**: APOSTA 5x5 - 5 R$, 10 R$, 15 R$, 20 R$, 50 R$, 100 R$

### 3. **Cores diferentes para identificação:**
- **2v2**: 🔵 Azul
- **3v3**: 🟢 Verde
- **4v4**: 🟠 Laranja
- **5v5**: 🟣 Roxo
- **Valorant**: 🔴 Vermelho

## 🔄 Fluxo de Funcionamento

### Exemplo para 3v3:
1. **Staff usa**: `!setup_3v3`
2. **Filas criadas** no canal #3v3 com cor verde
3. **Jogadores entram** nas filas 3v3
4. **Canal criado** na categoria #APOSTADO
5. **Mediação** aparece como "APOSTA 3x3 - X R$"
6. **Finalização** com `/finalizar`
7. **Log** normal (sem identificação especial de tipo)

## ⚙️ Características Técnicas

### **Filas Independentes:**
- Cada tipo (2v2, 3v3, 4v4, 5v5) tem suas próprias filas
- Jogador pode estar em apenas uma fila por tipo
- Sistemas não interferem entre si

### **Compatibilidade:**
- ✅ Comando `!setup_queues` ainda funciona (usa 5v5)
- ✅ Sistema antigo mantido para compatibilidade
- ✅ Valorant continua funcionando normalmente

### **Mediação Unificada:**
- Todas as apostas (2v2, 3v3, 4v4, 5v5) usam o mesmo canal de mediação
- Mesmo sistema de `/finalizar`
- Mesma categoria de canais de aposta

## 🚀 Para Hospedar

### Variáveis de ambiente adicionais:
```env
QUEUE_2V2_CHANNEL_ID=seu_canal_2v2
QUEUE_3V3_CHANNEL_ID=seu_canal_3v3
QUEUE_4V4_CHANNEL_ID=seu_canal_4v4
QUEUE_5V5_CHANNEL_ID=seu_canal_5v5
```

### Arquivos novos:
- ✅ `cogs/multi_queues.py` - Sistema de filas múltiplas
- ✅ `config.py` - Configurações atualizadas

## ⚠️ Importante

1. **Configure os IDs corretos** antes de usar os comandos
2. **Teste cada tipo** separadamente
3. **Use comandos específicos** para cada canal
4. **Mantenha a estrutura** de canais organizada

Agora você tem controle total sobre diferentes tipos de apostas! 🎉
