# cogs/ranks_teams.py

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import json
import os
from datetime import datetime
import config

# Arquivo para armazenar dados
DATA_FILE = "ranks_teams_data.json"

# Sistema de ranks Valorant com pontuações
VALORANT_RANKS = {
    "Iron 1": 1, "Iron 2": 2, "Iron 3": 3,
    "Bronze 1": 4, "Bronze 2": 5, "Bronze 3": 6,
    "Silver 1": 7, "Silver 2": 8, "Silver 3": 9,
    "Gold 1": 10, "Gold 2": 11, "Gold 3": 12,
    "Platinum 1": 13, "Platinum 2": 14, "Platinum 3": 15,
    "Diamond 1": 16, "Diamond 2": 17, "Diamond 3": 18,
    "Ascendant 1": 19, "Ascendant 2": 20, "Ascendant 3": 21,
    "Immortal 1": 22, "Immortal 2": 23, "Immortal 3": 24,
    "Radiant": 25
}

# Função para converter pontos de volta para rank
def points_to_rank(points):
    rounded_points = round(points)

    # Procura o rank correspondente aos pontos arredondados
    for rank, point in VALORANT_RANKS.items():
        if point == rounded_points:
            return rank

    # Se não encontrar exato, procura o mais próximo
    closest_rank = "Unranked"
    min_diff = float('inf')

    for rank, point in VALORANT_RANKS.items():
        diff = abs(point - points)
        if diff < min_diff:
            min_diff = diff
            closest_rank = rank

    return closest_rank

# Funções para gerenciar dados
def load_data():
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {"players": {}, "teams": {}}

def save_data(data):
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def get_player_data(user_id):
    data = load_data()
    return data["players"].get(str(user_id))

def get_team_data(team_id):
    data = load_data()
    return data["teams"].get(team_id)

def calculate_team_average(team_data):
    if not team_data or len(team_data["members"]) == 0:
        return 0, "Unranked"
    
    data = load_data()
    total_points = 0
    valid_members = 0
    
    for member_id in team_data["members"]:
        player = data["players"].get(str(member_id))
        if player and "rank_points" in player:
            total_points += player["rank_points"]
            valid_members += 1
    
    if valid_members == 0:
        return 0, "Unranked"
    
    average_points = total_points / valid_members
    average_rank = points_to_rank(average_points)
    return average_points, average_rank

def update_team_average(team_id):
    data = load_data()
    if team_id in data["teams"]:
        avg_points, avg_rank = calculate_team_average(data["teams"][team_id])
        data["teams"][team_id]["average_rank"] = avg_points
        data["teams"][team_id]["average_rank_name"] = avg_rank
        save_data(data)

async def notify_team_management_static(guild, team_id: str, action: str, user: discord.User):
    """Função estática para notificar o canal de gerenciamento do time."""
    try:
        from cogs.team_channels import load_team_channels_data
        team_channels_data = load_team_channels_data()

        if team_id in team_channels_data:
            manage_channel_id = team_channels_data[team_id]["manage_channel_id"]
            manage_channel = guild.get_channel(manage_channel_id)

            if manage_channel:
                if action == "join":
                    embed = discord.Embed(
                        title="✅ Novo Membro!",
                        description=f"{user.mention} **entrou** no time!",
                        color=discord.Color.green(),
                        timestamp=discord.utils.utcnow()
                    )
                    embed.set_thumbnail(url=user.display_avatar.url)

                elif action == "decline":
                    embed = discord.Embed(
                        title="❌ Convite Recusado",
                        description=f"{user.mention} **recusou** o convite para o time.",
                        color=discord.Color.red(),
                        timestamp=discord.utils.utcnow()
                    )
                    embed.set_thumbnail(url=user.display_avatar.url)

                elif action == "leave":
                    embed = discord.Embed(
                        title="👋 Membro Saiu",
                        description=f"{user.mention} **saiu** do time.",
                        color=discord.Color.orange(),
                        timestamp=discord.utils.utcnow()
                    )
                    embed.set_thumbnail(url=user.display_avatar.url)

                await manage_channel.send(embed=embed)
    except:
        pass  # Se não conseguir notificar, ignora

# Classes para botões no DM
class TeamInviteButton(Button):
    def __init__(self, action: str, team_id: str, team_name: str, guild_id: int):
        if action == "accept":
            super().__init__(label="✅ Aceitar", style=discord.ButtonStyle.green, custom_id=f"team_invite_accept_{team_id}")
        else:
            super().__init__(label="❌ Recusar", style=discord.ButtonStyle.red, custom_id=f"team_invite_decline_{team_id}")

        self.action = action
        self.team_id = team_id
        self.team_name = team_name
        self.guild_id = guild_id

    async def callback(self, interaction: discord.Interaction):
        user_id = str(interaction.user.id)

        # Pega o servidor
        guild = interaction.client.get_guild(self.guild_id)
        if not guild:
            await interaction.response.send_message("❌ Servidor não encontrado.", ephemeral=True)
            return

        data = load_data()

        if self.action == "accept":
            # Verifica se já está em um time
            player = data["players"].get(user_id)
            if player and "team_id" in player:
                await interaction.response.send_message("❌ Você já está em um time. Saia do time atual primeiro.", ephemeral=True)
                return

            # Verifica se o time ainda existe e tem o convite
            team = data["teams"].get(self.team_id)
            if not team:
                await interaction.response.send_message("❌ Este time não existe mais.", ephemeral=True)
                return

            if user_id not in team.get("invites", []):
                await interaction.response.send_message("❌ Você não tem convite pendente para este time.", ephemeral=True)
                return

            # Verifica se o time ainda tem vaga
            if len(team["members"]) >= 5:
                team["invites"].remove(user_id)
                save_data(data)
                await interaction.response.send_message("❌ Este time já está completo.", ephemeral=True)
                return

            # Aceita o convite
            team["members"].append(user_id)
            team["invites"].remove(user_id)

            # Atualiza dados do jogador
            if user_id not in data["players"]:
                data["players"][user_id] = {}
            data["players"][user_id]["team_id"] = self.team_id

            save_data(data)

            # Atualiza rank médio do time
            update_team_average(self.team_id)

            # Adiciona cargo do time
            member = guild.get_member(int(user_id))
            if member:
                try:
                    from cogs.team_channels import load_team_channels_data
                    team_channels_data = load_team_channels_data()

                    if self.team_id in team_channels_data:
                        role_id = team_channels_data[self.team_id]["role_id"]
                        role = guild.get_role(role_id)
                        if role:
                            await member.add_roles(role, reason="Entrou no time")
                except:
                    pass

            embed = discord.Embed(
                title="✅ Convite Aceito!",
                description=f"Você entrou no time **{self.team_name}**!",
                color=discord.Color.green()
            )
            embed.add_field(name="Membros", value=f"{len(team['members'])}/5", inline=True)

            # Desabilita os botões
            for item in self.view.children:
                item.disabled = True

            await interaction.response.edit_message(embed=embed, view=self.view)

            # Notifica no canal de gerenciamento
            await notify_team_management_static(guild, self.team_id, "join", interaction.user)

        else:  # decline
            # Remove o convite
            team = data["teams"].get(self.team_id)
            if team and user_id in team.get("invites", []):
                team["invites"].remove(user_id)
                save_data(data)

            embed = discord.Embed(
                title="❌ Convite Recusado",
                description=f"Você recusou o convite para o time **{self.team_name}**.",
                color=discord.Color.red()
            )

            # Desabilita os botões
            for item in self.view.children:
                item.disabled = True

            await interaction.response.edit_message(embed=embed, view=self.view)

            # Notifica no canal de gerenciamento
            await notify_team_management_static(guild, self.team_id, "decline", interaction.user)

class TeamInviteView(View):
    def __init__(self, team_id: str, team_name: str, guild_id: int):
        super().__init__(timeout=3600)  # 1 hora para responder

        self.add_item(TeamInviteButton("accept", team_id, team_name, guild_id))
        self.add_item(TeamInviteButton("decline", team_id, team_name, guild_id))

    async def on_timeout(self):
        # Desabilita os botões quando expira
        for item in self.children:
            item.disabled = True

    async def notify_team_management_removal(self, guild, team_id: str, removed_user: discord.Member, captain: discord.User):
        """Envia notificação quando um jogador é removido pelo capitão."""
        try:
            from cogs.team_channels import load_team_channels_data
            team_channels_data = load_team_channels_data()

            if team_id in team_channels_data:
                manage_channel_id = team_channels_data[team_id]["manage_channel_id"]
                manage_channel = guild.get_channel(manage_channel_id)

                if manage_channel:
                    embed = discord.Embed(
                        title="🚫 Membro Removido",
                        description=f"{removed_user.mention} foi **removido** do time pelo capitão {captain.mention}",
                        color=discord.Color.red(),
                        timestamp=discord.utils.utcnow()
                    )
                    embed.set_thumbnail(url=removed_user.display_avatar.url)

                    await manage_channel.send(embed=embed)
        except:
            pass  # Se não conseguir notificar, ignora

async def update_user_rank_role(guild, user, new_rank):
    """
    Atualiza o cargo de rank do usuário.
    Remove o cargo de rank antigo e adiciona o novo.
    """
    if not guild or not user:
        return False, "Usuário ou servidor não encontrado"

    # Pega todos os cargos de rank configurados
    rank_role_ids = [role_id for role_id in config.RANK_ROLES.values() if role_id is not None]

    # Remove todos os cargos de rank antigos
    roles_to_remove = []
    for role in user.roles:
        if role.id in rank_role_ids:
            roles_to_remove.append(role)

    if roles_to_remove:
        try:
            await user.remove_roles(*roles_to_remove, reason="Atualizando rank")
        except discord.Forbidden:
            return False, "Bot não tem permissão para remover cargos"
        except discord.HTTPException:
            return False, "Erro ao remover cargos antigos"

    # Adiciona o novo cargo de rank
    new_role_id = config.RANK_ROLES.get(new_rank)
    if new_role_id:
        new_role = guild.get_role(new_role_id)
        if new_role:
            try:
                await user.add_roles(new_role, reason=f"Rank atualizado para {new_rank}")
                return True, f"Cargo {new_rank} adicionado"
            except discord.Forbidden:
                return False, "Bot não tem permissão para adicionar cargos"
            except discord.HTTPException:
                return False, "Erro ao adicionar novo cargo"
        else:
            return False, f"Cargo {new_rank} não encontrado no servidor"
    else:
        return False, f"ID do cargo {new_rank} não configurado"

class RankSelect(discord.ui.Select):
    def __init__(self):
        options = []
        for rank in VALORANT_RANKS.keys():
            options.append(discord.SelectOption(label=rank, value=rank))
        
        super().__init__(placeholder="Escolha seu rank atual...", options=options)

    async def callback(self, interaction: discord.Interaction):
        selected_rank = self.values[0]
        user_id = str(interaction.user.id)

        data = load_data()

        # Registra ou atualiza o rank do jogador
        if user_id not in data["players"]:
            data["players"][user_id] = {}

        data["players"][user_id].update({
            "rank": selected_rank,
            "rank_points": VALORANT_RANKS[selected_rank],
            "updated_at": datetime.now().isoformat()
        })

        save_data(data)

        # Se o jogador está em um time, atualiza a média do time (após salvar)
        if "team_id" in data["players"][user_id]:
            team_id = data["players"][user_id]["team_id"]
            update_team_average(team_id)

        # Atualiza o cargo de rank do usuário
        role_success, role_message = await update_user_rank_role(interaction.guild, interaction.user, selected_rank)

        embed = discord.Embed(
            title="✅ Rank Registrado!",
            description=f"Seu rank foi definido como **{selected_rank}**",
            color=discord.Color.green()
        )

        if role_success:
            embed.add_field(name="Cargo", value=f"✅ {role_message}", inline=False)
        else:
            embed.add_field(name="Cargo", value=f"⚠️ {role_message}", inline=False)

        await interaction.response.send_message(embed=embed, ephemeral=True)

class RankSelectView(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=300)
        self.add_item(RankSelect())

class RanksTeamsCog(commands.Cog, name="RanksTeams"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra views persistentes para convites de time
        if not hasattr(self.bot, '_team_invite_views_registered'):
            # Carrega dados dos times para registrar views existentes
            data = load_data()
            for team_id, team_data in data["teams"].items():
                if "invites" in team_data and team_data["invites"]:
                    # Registra view para cada time que tem convites pendentes
                    view = TeamInviteView(team_id, team_data["name"], config.GUILD_ID)
                    self.bot.add_view(view)

            self.bot._team_invite_views_registered = True
        print("Ranks Teams Cog is ready. Team invite views registered.")

    @app_commands.command(name="registrar_rank", description="Registre ou atualize seu rank do Valorant")
    async def register_rank(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title="📊 Registro de Rank",
            description="Selecione seu rank atual do Valorant:",
            color=discord.Color.blue()
        )
        view = RankSelectView()
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    @app_commands.command(name="meu_rank", description="Veja seu rank atual")
    async def my_rank(self, interaction: discord.Interaction):
        player = get_player_data(interaction.user.id)
        
        if not player or "rank" not in player:
            embed = discord.Embed(
                title="❌ Rank não encontrado",
                description="Você ainda não registrou seu rank. Use `/registrar_rank`",
                color=discord.Color.red()
            )
        else:
            embed = discord.Embed(
                title="📊 Seu Rank",
                description=f"**{player['rank']}** ({player['rank_points']} pontos)",
                color=discord.Color.blue()
            )
            if "updated_at" in player:
                embed.add_field(
                    name="Última atualização",
                    value=f"<t:{int(datetime.fromisoformat(player['updated_at']).timestamp())}:R>",
                    inline=False
                )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="rank", description="Veja o rank de outro jogador")
    async def check_rank(self, interaction: discord.Interaction, jogador: discord.Member):
        player = get_player_data(jogador.id)

        if not player or "rank" not in player:
            embed = discord.Embed(
                title="❌ Rank não encontrado",
                description=f"{jogador.mention} ainda não registrou seu rank.",
                color=discord.Color.red()
            )
        else:
            embed = discord.Embed(
                title=f"📊 Rank de {jogador.display_name}",
                description=f"**{player['rank']}** ({player['rank_points']} pontos)",
                color=discord.Color.blue()
            )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    # ==================== COMANDOS DE TIMES ====================

    @app_commands.command(name="criar_time", description="Crie um novo time")
    async def create_team(self, interaction: discord.Interaction, nome: str):
        # Defer a resposta para dar mais tempo
        await interaction.response.defer(ephemeral=True)

        if len(nome) > 20:
            await interaction.followup.send("❌ Nome do time deve ter no máximo 20 caracteres.", ephemeral=True)
            return

        user_id = str(interaction.user.id)
        data = load_data()

        # Verifica se o jogador tem rank
        if user_id not in data["players"] or "rank" not in data["players"][user_id]:
            await interaction.followup.send("❌ Você precisa registrar seu rank primeiro: `/registrar_rank`", ephemeral=True)
            return

        # Verifica se já está em um time
        if "team_id" in data["players"][user_id]:
            await interaction.followup.send("❌ Você já está em um time. Use `/sair_time` primeiro.", ephemeral=True)
            return

        # Verifica se o nome já existe
        for team in data["teams"].values():
            if team["name"].lower() == nome.lower():
                await interaction.followup.send("❌ Já existe um time com esse nome. Escolha outro.", ephemeral=True)
                return

        # Cria o time
        team_id = f"team_{len(data['teams']) + 1}_{interaction.user.id}"
        data["teams"][team_id] = {
            "name": nome,
            "captain": user_id,
            "members": [user_id],
            "created_at": datetime.now().isoformat(),
            "invites": []
        }

        # Adiciona o jogador ao time
        data["players"][user_id]["team_id"] = team_id

        save_data(data)

        # Calcula rank médio inicial (após salvar os dados)
        update_team_average(team_id)

        # Cria canais e cargo do time
        team_channels_cog = self.bot.get_cog("TeamChannels")
        if team_channels_cog:
            success, result = await team_channels_cog.create_team_channels(
                interaction.guild, nome, team_id, interaction.user.id
            )

            if success:
                # Adiciona o cargo do time ao capitão
                team_role = result["role"]
                try:
                    await interaction.user.add_roles(team_role, reason="Capitão do time")
                except:
                    pass  # Se não conseguir adicionar, ignora

        # Pega dados atualizados para mostrar o rank
        updated_team = get_team_data(team_id)

        embed = discord.Embed(
            title="✅ Time Criado!",
            description=f"Time **{nome}** criado com sucesso!\nVocê é o capitão.",
            color=discord.Color.green()
        )
        embed.add_field(name="Membros", value="1/5", inline=True)
        embed.add_field(name="Rank Médio", value=updated_team.get("average_rank_name", "Calculando..."), inline=True)

        if team_channels_cog and success:
            embed.add_field(
                name="🏆 Canais Criados",
                value=f"• {result['manage_channel'].mention} - Gerenciamento\n• {result['chat_channel'].mention} - Chat\n• {result['voice_channel'].mention} - Voz",
                inline=False
            )

        embed.add_field(name="Próximo passo", value="Use `/convidar @jogador` para convidar outros jogadores", inline=False)

        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="meu_time", description="Veja informações do seu time")
    async def my_team(self, interaction: discord.Interaction):
        user_id = str(interaction.user.id)
        player = get_player_data(interaction.user.id)

        if not player or "team_id" not in player:
            await interaction.response.send_message("❌ Você não está em nenhum time. Use `/criar_time` ou `/procurar_times`", ephemeral=True)
            return

        team = get_team_data(player["team_id"])
        if not team:
            await interaction.response.send_message("❌ Erro: Time não encontrado.", ephemeral=True)
            return

        data = load_data()

        # Lista membros com ranks
        members_info = []
        for member_id in team["members"]:
            member = self.bot.get_user(int(member_id))
            player_data = data["players"].get(member_id, {})
            rank = player_data.get("rank", "Sem rank")

            role = "👑" if member_id == team["captain"] else "👤"
            members_info.append(f"{role} {member.mention if member else 'Usuário não encontrado'} - {rank}")

        # Lista convites pendentes
        invites_info = []
        for invite_id in team.get("invites", []):
            user = self.bot.get_user(int(invite_id))
            invites_info.append(f"⏳ {user.mention if user else 'Usuário não encontrado'}")

        embed = discord.Embed(
            title=f"🏆 Time: {team['name']}",
            color=discord.Color.blue()
        )

        embed.add_field(
            name=f"Membros ({len(team['members'])}/5)",
            value="\n".join(members_info) if members_info else "Nenhum membro",
            inline=False
        )

        if invites_info:
            embed.add_field(
                name="Convites Pendentes",
                value="\n".join(invites_info),
                inline=False
            )

        if len(team["members"]) > 0:
            embed.add_field(
                name="Rank Médio",
                value=team.get("average_rank_name", "Calculando..."),
                inline=True
            )

        embed.add_field(
            name="Status",
            value="✅ Completo" if len(team["members"]) == 5 else f"⏳ Precisa de {5 - len(team['members'])} jogadores",
            inline=True
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="convidar", description="Convide um jogador para seu time (apenas capitão)")
    async def invite_player(self, interaction: discord.Interaction, jogador: discord.Member):
        user_id = str(interaction.user.id)
        target_id = str(jogador.id)

        if user_id == target_id:
            await interaction.response.send_message("❌ Você não pode convidar a si mesmo.", ephemeral=True)
            return

        data = load_data()
        player = data["players"].get(user_id)

        # Verifica se está em um time e é capitão
        if not player or "team_id" not in player:
            await interaction.response.send_message("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team = data["teams"].get(player["team_id"])
        if not team or team["captain"] != user_id:
            await interaction.response.send_message("❌ Apenas o capitão pode convidar jogadores.", ephemeral=True)
            return

        # Verifica se o time já está completo
        if len(team["members"]) >= 5:
            await interaction.response.send_message("❌ Seu time já está completo (5/5).", ephemeral=True)
            return

        # Verifica se o jogador tem rank
        target_player = data["players"].get(target_id)
        if not target_player or "rank" not in target_player:
            await interaction.response.send_message(f"❌ {jogador.mention} precisa registrar o rank primeiro.", ephemeral=True)
            return

        # Verifica se já está em um time
        if "team_id" in target_player:
            await interaction.response.send_message(f"❌ {jogador.mention} já está em outro time.", ephemeral=True)
            return

        # Verifica se já foi convidado
        if target_id in team.get("invites", []):
            await interaction.response.send_message(f"❌ {jogador.mention} já foi convidado para este time.", ephemeral=True)
            return

        # Adiciona o convite
        if "invites" not in team:
            team["invites"] = []
        team["invites"].append(target_id)
        save_data(data)

        # Notifica o convidado com botões
        try:
            embed_invite = discord.Embed(
                title="🎉 Convite para Time!",
                description=f"Você foi convidado para o time **{team['name']}**",
                color=discord.Color.gold()
            )
            embed_invite.add_field(name="Capitão", value=interaction.user.display_name, inline=True)
            embed_invite.add_field(name="Membros atuais", value=f"{len(team['members'])}/5", inline=True)
            embed_invite.add_field(name="Servidor", value=interaction.guild.name, inline=True)

            # Cria view com botões
            view = TeamInviteView(player["team_id"], team["name"], interaction.guild.id)
            await jogador.send(embed=embed_invite, view=view)
        except:
            pass  # Se não conseguir mandar DM, ignora

        embed = discord.Embed(
            title="✅ Convite Enviado!",
            description=f"Convite enviado para {jogador.mention}",
            color=discord.Color.green()
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="aceitar_convite", description="Aceite um convite de time")
    async def accept_invite(self, interaction: discord.Interaction):
        user_id = str(interaction.user.id)
        data = load_data()

        # Verifica se já está em um time
        player = data["players"].get(user_id)
        if player and "team_id" in player:
            await interaction.response.send_message("❌ Você já está em um time. Use `/sair_time` primeiro.", ephemeral=True)
            return

        # Procura convites pendentes
        team_found = None
        for team_id, team in data["teams"].items():
            if user_id in team.get("invites", []):
                team_found = (team_id, team)
                break

        if not team_found:
            await interaction.response.send_message("❌ Você não tem convites pendentes.", ephemeral=True)
            return

        team_id, team = team_found

        # Verifica se o time ainda tem vaga
        if len(team["members"]) >= 5:
            # Remove o convite se o time estiver cheio
            team["invites"].remove(user_id)
            save_data(data)
            await interaction.response.send_message("❌ Este time já está completo.", ephemeral=True)
            return

        # Adiciona ao time
        team["members"].append(user_id)
        team["invites"].remove(user_id)

        # Atualiza dados do jogador
        if user_id not in data["players"]:
            data["players"][user_id] = {}
        data["players"][user_id]["team_id"] = team_id

        save_data(data)

        # Atualiza rank médio do time (após salvar)
        update_team_average(team_id)

        # Adiciona cargo do time ao novo membro
        await self.add_team_role_to_member(interaction.guild, interaction.user.id, team_id)

        # Pega dados atualizados
        updated_team = get_team_data(team_id)

        embed = discord.Embed(
            title="✅ Convite Aceito!",
            description=f"Você entrou no time **{team['name']}**!",
            color=discord.Color.green()
        )
        embed.add_field(name="Membros", value=f"{len(team['members'])}/5", inline=True)
        embed.add_field(name="Rank Médio", value=updated_team.get("average_rank_name", "Calculando..."), inline=True)

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="sair_time", description="Saia do seu time atual")
    async def leave_team(self, interaction: discord.Interaction):
        # Defer a resposta para dar mais tempo (remoção de cargo pode demorar)
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)
        data = load_data()

        player = data["players"].get(user_id)
        if not player or "team_id" not in player:
            await interaction.followup.send("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team_id = player["team_id"]
        team = data["teams"].get(team_id)

        if not team:
            await interaction.followup.send("❌ Erro: Time não encontrado.", ephemeral=True)
            return

        team_name = team["name"]

        # Se é o capitão e há outros membros, não pode sair
        if team["captain"] == user_id and len(team["members"]) > 1:
            await interaction.followup.send("❌ Você é o capitão e há outros membros. Use `/promover @jogador` primeiro ou `/dissolver_time`.", ephemeral=True)
            return

        # Remove do time
        team["members"].remove(user_id)
        del player["team_id"]

        save_data(data)

        # Se era o último membro, dissolve o time automaticamente
        if len(team["members"]) == 0:
            # Remove o time dos dados salvos também
            data = load_data()
            if team_id in data["teams"]:
                del data["teams"][team_id]
                save_data(data)

            # Remove canais e cargo do time
            await self.delete_team_channels_and_role(interaction.guild, team_id)
        else:
            # Atualiza rank médio (após salvar)
            update_team_average(team_id)

            # Notifica no canal de gerenciamento (só se o time não foi dissolvido)
            await notify_team_management_static(interaction.guild, team_id, "leave", interaction.user)

        if len(team["members"]) == 0:
            embed = discord.Embed(
                title="✅ Time Dissolvido",
                description=f"Você era o último membro do time **{team_name}**\n• Time foi dissolvido automaticamente\n• Canais removidos\n• Cargo deletado",
                color=discord.Color.red()
            )
        else:
            embed = discord.Embed(
                title="✅ Saiu do Time",
                description=f"Você saiu do time **{team_name}**",
                color=discord.Color.orange()
            )
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="remover", description="Remova um jogador do time (apenas capitão)")
    async def remove_player(self, interaction: discord.Interaction, jogador: discord.Member):
        # Defer a resposta para dar mais tempo (remoção de cargo pode demorar)
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)
        target_id = str(jogador.id)

        if user_id == target_id:
            await interaction.followup.send("❌ Use `/sair_time` para sair do time.", ephemeral=True)
            return

        data = load_data()
        player = data["players"].get(user_id)

        if not player or "team_id" not in player:
            await interaction.followup.send("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team = data["teams"].get(player["team_id"])
        if not team or team["captain"] != user_id:
            await interaction.followup.send("❌ Apenas o capitão pode remover jogadores.", ephemeral=True)
            return

        if target_id not in team["members"]:
            await interaction.followup.send("❌ Este jogador não está no seu time.", ephemeral=True)
            return

        # Remove o jogador
        team["members"].remove(target_id)
        target_player = data["players"].get(target_id)
        if target_player and "team_id" in target_player:
            del target_player["team_id"]

        save_data(data)

        # Remove cargo do time do jogador removido
        await self.remove_team_role_from_member(interaction.guild, int(target_id), player["team_id"])

        # Atualiza rank médio (após salvar)
        update_team_average(player["team_id"])

        # Notifica no canal de gerenciamento
        await self.notify_team_management_removal(interaction.guild, player["team_id"], jogador, interaction.user)

        # Pega dados atualizados
        updated_team = get_team_data(player["team_id"])

        embed = discord.Embed(
            title="✅ Jogador Removido",
            description=f"{jogador.mention} foi removido do time **{team['name']}**",
            color=discord.Color.orange()
        )
        embed.add_field(name="Membros restantes", value=f"{len(team['members'])}/5", inline=True)
        embed.add_field(name="Novo Rank Médio", value=updated_team.get("average_rank_name", "Calculando..."), inline=True)
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="dissolver_time", description="Dissolva seu time (apenas capitão)")
    async def dissolve_team(self, interaction: discord.Interaction):
        # Defer a resposta para dar mais tempo
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)
        data = load_data()

        player = data["players"].get(user_id)
        if not player or "team_id" not in player:
            await interaction.followup.send("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team_id = player["team_id"]
        team = data["teams"].get(team_id)

        if not team or team["captain"] != user_id:
            await interaction.followup.send("❌ Apenas o capitão pode dissolver o time.", ephemeral=True)
            return

        team_name = team["name"]

        # Remove todos os membros do time
        for member_id in team["members"]:
            member_data = data["players"].get(member_id)
            if member_data and "team_id" in member_data:
                del member_data["team_id"]

            # Remove cargo do time de cada membro
            await self.remove_team_role_from_member(interaction.guild, int(member_id), team_id)

        # Remove o time
        del data["teams"][team_id]
        save_data(data)

        # Remove canais e cargo do time
        await self.delete_team_channels_and_role(interaction.guild, team_id)

        embed = discord.Embed(
            title="✅ Time Dissolvido",
            description=f"Time **{team_name}** foi dissolvido\n• Canais removidos\n• Cargo deletado\n• Membros removidos",
            color=discord.Color.red()
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="promover", description="Promove um jogador a capitão do time")
    async def promote_player(self, interaction: discord.Interaction, jogador: discord.Member):
        user_id = str(interaction.user.id)
        target_id = str(jogador.id)

        if user_id == target_id:
            await interaction.response.send_message("❌ Você não pode promover a si mesmo.", ephemeral=True)
            return

        data = load_data()
        player = data["players"].get(user_id)

        if not player or "team_id" not in player:
            await interaction.response.send_message("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team_id = player["team_id"]
        team = data["teams"].get(team_id)

        if not team or team["captain"] != user_id:
            await interaction.response.send_message("❌ Apenas o capitão pode promover jogadores.", ephemeral=True)
            return

        if target_id not in team["members"]:
            await interaction.response.send_message("❌ Este jogador não está no seu time.", ephemeral=True)
            return

        # Verifica se o jogador alvo tem rank
        target_player = data["players"].get(target_id)
        if not target_player or "rank" not in target_player:
            await interaction.response.send_message(f"❌ {jogador.mention} precisa ter rank registrado para ser capitão.", ephemeral=True)
            return

        # Promove o jogador
        old_captain = interaction.user.mention
        team["captain"] = target_id
        save_data(data)

        embed = discord.Embed(
            title="👑 Novo Capitão!",
            description=f"{jogador.mention} agora é o capitão do time **{team['name']}**",
            color=discord.Color.gold()
        )
        embed.add_field(name="Capitão Anterior", value=old_captain, inline=True)
        embed.add_field(name="Novo Capitão", value=jogador.mention, inline=True)
        embed.add_field(
            name="📋 Responsabilidades",
            value="• Convidar/remover jogadores\n• Publicar vagas\n• Dissolver time",
            inline=False
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)

        # Notifica o novo capitão por DM
        try:
            dm_embed = discord.Embed(
                title="👑 Você foi promovido!",
                description=f"Agora você é o capitão do time **{team['name']}**",
                color=discord.Color.gold()
            )
            dm_embed.add_field(
                name="Seus novos poderes:",
                value="• `/convidar @jogador`\n• `/remover @jogador`\n• `/publicar_vaga`\n• `/dissolver_time`",
                inline=False
            )
            await jogador.send(embed=dm_embed)
        except:
            pass  # Se não conseguir mandar DM, ignora

    @app_commands.command(name="recalcular_rank", description="Recalcula o rank médio do seu time (debug)")
    async def recalculate_rank(self, interaction: discord.Interaction):
        user_id = str(interaction.user.id)
        player = get_player_data(interaction.user.id)

        if not player or "team_id" not in player:
            await interaction.response.send_message("❌ Você não está em nenhum time.", ephemeral=True)
            return

        team_id = player["team_id"]
        team = get_team_data(team_id)

        if not team:
            await interaction.response.send_message("❌ Time não encontrado.", ephemeral=True)
            return

        # Força o recálculo
        update_team_average(team_id)

        # Pega os dados atualizados
        updated_team = get_team_data(team_id)

        embed = discord.Embed(
            title="🔄 Rank Recalculado",
            description=f"Time **{team['name']}**",
            color=discord.Color.blue()
        )
        embed.add_field(
            name="Rank Médio Atualizado",
            value=updated_team.get("average_rank_name", "Erro no cálculo"),
            inline=True
        )
        embed.add_field(
            name="Pontos Médios",
            value=f"{updated_team.get('average_rank', 0):.1f}",
            inline=True
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="criar_cargos_rank", description="Cria todos os cargos de rank automaticamente (apenas staff)")
    @app_commands.checks.has_role(config.STAFF_ROLE_ID)
    async def create_rank_roles(self, interaction: discord.Interaction):
        """Cria todos os cargos de rank do Valorant automaticamente."""
        guild = interaction.guild

        await interaction.response.defer(ephemeral=True)

        # Cores para cada tier de rank
        rank_colors = {
            "Iron": discord.Color.from_rgb(74, 65, 42),      # Marrom escuro
            "Bronze": discord.Color.from_rgb(205, 127, 50),   # Bronze
            "Silver": discord.Color.from_rgb(192, 192, 192),  # Prata
            "Gold": discord.Color.from_rgb(255, 215, 0),      # Dourado
            "Platinum": discord.Color.from_rgb(229, 228, 226), # Platina
            "Diamond": discord.Color.from_rgb(185, 242, 255), # Azul claro
            "Ascendant": discord.Color.from_rgb(0, 255, 127), # Verde
            "Immortal": discord.Color.from_rgb(186, 85, 211), # Roxo
            "Radiant": discord.Color.from_rgb(255, 255, 224)  # Amarelo claro
        }

        created_roles = []
        errors = []

        for rank_name in VALORANT_RANKS.keys():
            try:
                # Determina a cor baseada no tier
                tier = rank_name.split()[0]  # "Gold 2" -> "Gold"
                color = rank_colors.get(tier, discord.Color.default())

                # Cria o cargo
                role = await guild.create_role(
                    name=rank_name,
                    color=color,
                    reason="Cargo de rank criado automaticamente"
                )

                created_roles.append(f"✅ {rank_name} (ID: {role.id})")

            except discord.Forbidden:
                errors.append(f"❌ {rank_name} - Sem permissão")
            except discord.HTTPException as e:
                errors.append(f"❌ {rank_name} - Erro: {str(e)}")

        # Monta a resposta
        embed = discord.Embed(
            title="🎖️ Criação de Cargos de Rank",
            color=discord.Color.blue()
        )

        if created_roles:
            embed.add_field(
                name=f"Cargos Criados ({len(created_roles)})",
                value="\n".join(created_roles[:10]) + ("\n..." if len(created_roles) > 10 else ""),
                inline=False
            )

        if errors:
            embed.add_field(
                name=f"Erros ({len(errors)})",
                value="\n".join(errors[:5]) + ("\n..." if len(errors) > 5 else ""),
                inline=False
            )

        embed.add_field(
            name="⚠️ Próximo Passo",
            value="Configure os IDs dos cargos no `config.py` na variável `RANK_ROLES`",
            inline=False
        )

        await interaction.followup.send(embed=embed, ephemeral=True)

    async def add_team_role_to_member(self, guild, user_id: int, team_id: str):
        """Adiciona o cargo do time ao membro."""
        try:
            from cogs.team_channels import load_team_channels_data
            team_channels_data = load_team_channels_data()

            if team_id in team_channels_data:
                role_id = team_channels_data[team_id]["role_id"]
                role = guild.get_role(role_id)
                user = guild.get_member(user_id)

                if role and user:
                    await user.add_roles(role, reason="Entrou no time")
        except:
            pass  # Se não conseguir, ignora

    async def remove_team_role_from_member(self, guild, user_id: int, team_id: str):
        """Remove o cargo do time do membro."""
        try:
            from cogs.team_channels import load_team_channels_data
            team_channels_data = load_team_channels_data()

            if team_id in team_channels_data:
                role_id = team_channels_data[team_id]["role_id"]
                role = guild.get_role(role_id)
                user = guild.get_member(user_id)

                if role and user:
                    await user.remove_roles(role, reason="Saiu do time")
        except:
            pass  # Se não conseguir, ignora

    async def delete_team_channels_and_role(self, guild, team_id: str):
        """Deleta todos os canais e cargo do time."""
        try:
            from cogs.team_channels import load_team_channels_data, save_team_channels_data
            team_channels_data = load_team_channels_data()

            if team_id in team_channels_data:
                team_info = team_channels_data[team_id]

                # Deleta canais
                channels_to_delete = [
                    team_info.get("manage_channel_id"),
                    team_info.get("chat_channel_id"),
                    team_info.get("voice_channel_id")
                ]

                for channel_id in channels_to_delete:
                    if channel_id:
                        channel = guild.get_channel(channel_id)
                        if channel:
                            try:
                                await channel.delete(reason="Time dissolvido")
                            except:
                                pass

                # Deleta cargo
                role_id = team_info.get("role_id")
                if role_id:
                    role = guild.get_role(role_id)
                    if role:
                        try:
                            await role.delete(reason="Time dissolvido")
                        except:
                            pass

                # Remove dados dos canais
                del team_channels_data[team_id]
                save_team_channels_data(team_channels_data)

        except:
            pass  # Se não conseguir, ignora

async def setup(bot: commands.Bot):
    await bot.add_cog(RanksTeamsCog(bot), guild=discord.Object(id=config.GUILD_ID))
