# cogs/rematch_ranks_teams.py

import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import json
import os
from datetime import datetime
import config

# Arquivo de dados separado para REMATCH
REMATCH_DATA_FILE = "rematch_data.json"

# Ranks do REMATCH (sistema próprio)
REMATCH_RANKS = [
    ("Bronze 3", 1), ("Bronze 2", 2), ("Bronze 1", 3),
    ("Silver 3", 4), ("Silver 2", 5), ("Silver 1", 6),
    ("Gold 3", 7), ("Gold 2", 8), ("Gold 1", 9),
    ("Platinum 3", 10), ("Platinum 2", 11), ("Platinum 1", 12),
    ("Diamond 3", 13), ("Diamond 2", 14), ("Diamond 1", 15),
    ("Elite 3", 16), ("Elite 2", 17), ("Elite 1", 18)
]

def load_rematch_data():
    if os.path.exists(REMATCH_DATA_FILE):
        with open(R<PERSON><PERSON>CH_DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {"players": {}, "teams": {}}

def save_rematch_data(data):
    with open(REMATCH_DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def get_rematch_player_data(user_id):
    data = load_rematch_data()
    return data["players"].get(str(user_id))

def get_rematch_team_data(team_id):
    data = load_rematch_data()
    return data["teams"].get(team_id)

def calculate_rematch_team_average(team):
    if not team["members"]:
        return 0, "Sem rank"
    
    data = load_rematch_data()
    total_points = 0
    valid_members = 0
    
    for member_id in team["members"]:
        player = data["players"].get(member_id)
        if player and "rank_points" in player:
            total_points += player["rank_points"]
            valid_members += 1
    
    if valid_members == 0:
        return 0, "Sem rank"
    
    avg_points = total_points / valid_members
    
    # Encontra o rank mais próximo
    for rank_name, rank_points in REMATCH_RANKS:
        if avg_points <= rank_points:
            return avg_points, rank_name
    
    return avg_points, "Challenger"

def update_rematch_team_average(team_id):
    data = load_rematch_data()
    if team_id in data["teams"]:
        avg_points, avg_rank = calculate_rematch_team_average(data["teams"][team_id])
        data["teams"][team_id]["average_rank"] = avg_points
        data["teams"][team_id]["average_rank_name"] = avg_rank
        save_rematch_data(data)

async def register_rematch_player_rank(user_id: int, rank_name: str, rank_points: int):
    """Função auxiliar para registrar rank REMATCH de um jogador."""
    try:
        user_str = str(user_id)
        data = load_rematch_data()

        # Atualiza ou cria dados do jogador
        if user_str not in data["players"]:
            data["players"][user_str] = {}

        old_rank = data["players"][user_str].get("rank", "Nenhum")
        data["players"][user_str]["rank"] = rank_name
        data["players"][user_str]["rank_points"] = rank_points
        data["players"][user_str]["updated_at"] = datetime.now().isoformat()

        save_rematch_data(data)

        # Se o jogador está em um time, recalcula o rank médio
        if "team_id" in data["players"][user_str]:
            team_id = data["players"][user_str]["team_id"]
            update_rematch_team_average(team_id)

        if old_rank == "Nenhum":
            return True, f"Rank REMATCH {rank_name} registrado com sucesso!"
        else:
            return True, f"Rank REMATCH atualizado de {old_rank} para {rank_name}!"

    except Exception as e:
        return False, f"Erro ao registrar rank REMATCH: {str(e)}"

async def update_rematch_rank_role(guild, user_id: int, new_rank: str, old_rank: str = None):
    """Atualiza o cargo de rank REMATCH do jogador."""
    try:
        member = guild.get_member(user_id)
        if not member:
            return

        # Remove cargo antigo se existir
        if old_rank and old_rank != "Nenhum":
            old_role_id = config.REMATCH_RANK_ROLES.get(old_rank)
            if old_role_id:
                old_role = guild.get_role(old_role_id)
                if old_role and old_role in member.roles:
                    await member.remove_roles(old_role, reason="Rank REMATCH atualizado")
                    print(f"🔄 REMATCH: Removido cargo {old_rank} de {member.display_name}")

        # Adiciona novo cargo
        new_role_id = config.REMATCH_RANK_ROLES.get(new_rank)
        if new_role_id:
            new_role = guild.get_role(new_role_id)
            if new_role and new_role not in member.roles:
                await member.add_roles(new_role, reason="Rank REMATCH registrado")
                print(f"✅ REMATCH: Adicionado cargo {new_rank} para {member.display_name}")
        else:
            print(f"⚠️ REMATCH: Cargo para rank {new_rank} não configurado no config.py")

    except Exception as e:
        print(f"❌ REMATCH: Erro ao atualizar cargo de rank: {e}")

async def create_rematch_team_for_user(interaction, team_name: str):
    """Função auxiliar para criar time REMATCH via interface."""
    try:
        user_id = str(interaction.user.id)
        data = load_rematch_data()
        
        # Gera ID único para o time REMATCH
        team_id = f"rematch_team_{len(data['teams']) + 1}_{user_id}"
        
        # Cria o time REMATCH
        data["teams"][team_id] = {
            "name": team_name,
            "captain": user_id,
            "members": [user_id],
            "created_at": datetime.now().isoformat(),
            "invites": [],
            "game_type": "REMATCH"  # Identificador do jogo
        }
        
        # Atualiza dados do jogador
        data["players"][user_id]["team_id"] = team_id
        
        save_rematch_data(data)
        
        # Calcula rank médio inicial
        update_rematch_team_average(team_id)
        
        # Cria canais e cargo do time REMATCH
        rematch_channels_cog = interaction.client.get_cog("RematchChannels")
        if rematch_channels_cog:
            success, result = await rematch_channels_cog.create_rematch_team_channels(
                interaction.guild, team_name, team_id, interaction.user.id
            )

            if success:
                # Adiciona o cargo do time ao capitão
                team_role = result["role"]
                try:
                    await interaction.user.add_roles(team_role, reason="Capitão do time REMATCH")
                except:
                    pass

                return True, {"channels": result}

        return True, {"message": "Time REMATCH criado com sucesso!"}
        
    except Exception as e:
        return False, str(e)

class RematchRanksTeamsCog(commands.Cog, name="RematchRanksTeams"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="registrar_rank_rematch", description="Registre seu rank no REMATCH")
    async def register_rematch_rank(self, interaction: discord.Interaction, rank: str):
        await interaction.response.defer(ephemeral=True)
        
        # Verifica se o rank é válido
        rank_points = None
        for rank_name, points in REMATCH_RANKS:
            if rank_name.lower() == rank.lower():
                rank_points = points
                rank = rank_name
                break
        
        if rank_points is None:
            ranks_list = ", ".join([r[0] for r in REMATCH_RANKS])
            await interaction.followup.send(f"❌ Rank inválido. Ranks disponíveis: {ranks_list}", ephemeral=True)
            return
        
        user_id = str(interaction.user.id)
        data = load_rematch_data()
        
        # Atualiza ou cria dados do jogador
        if user_id not in data["players"]:
            data["players"][user_id] = {}
        
        old_rank = data["players"][user_id].get("rank", "Nenhum")
        data["players"][user_id]["rank"] = rank
        data["players"][user_id]["rank_points"] = rank_points
        data["players"][user_id]["updated_at"] = datetime.now().isoformat()
        
        save_rematch_data(data)

        # Atualiza cargo de rank
        await update_rematch_rank_role(interaction.guild, interaction.user.id, rank, old_rank)

        # Se o jogador está em um time, recalcula o rank médio
        if "team_id" in data["players"][user_id]:
            team_id = data["players"][user_id]["team_id"]
            update_rematch_team_average(team_id)

        embed = discord.Embed(
            title="✅ Rank REMATCH Registrado!",
            color=discord.Color.green()
        )

        if old_rank == "Nenhum":
            embed.description = f"Seu rank **{rank}** foi registrado no REMATCH!\n🎖️ Cargo atualizado automaticamente!"
        else:
            embed.description = f"Rank atualizado de **{old_rank}** para **{rank}**!\n🎖️ Cargo atualizado automaticamente!"
        
        embed.add_field(
            name="📊 Próximos passos",
            value="• Crie um time com `/criar_time_rematch`\n• Ou seja convidado para um time REMATCH",
            inline=False
        )
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="criar_time_rematch", description="Crie um novo time REMATCH")
    async def create_rematch_team(self, interaction: discord.Interaction, nome: str):
        await interaction.response.defer(ephemeral=True)
        
        if len(nome) > 20:
            await interaction.followup.send("❌ Nome do time deve ter no máximo 20 caracteres.", ephemeral=True)
            return
        
        user_id = str(interaction.user.id)
        data = load_rematch_data()
        
        # Verifica se o jogador tem rank REMATCH
        if user_id not in data["players"] or "rank" not in data["players"][user_id]:
            await interaction.followup.send("❌ Você precisa registrar seu rank REMATCH primeiro: `/registrar_rank_rematch`", ephemeral=True)
            return
        
        # Verifica se já está em um time REMATCH
        if "team_id" in data["players"][user_id]:
            await interaction.followup.send("❌ Você já está em um time REMATCH. Use `/sair_time_rematch` primeiro.", ephemeral=True)
            return
        
        # Verifica se o nome já existe
        for team in data["teams"].values():
            if team["name"].lower() == nome.lower():
                await interaction.followup.send("❌ Já existe um time REMATCH com esse nome. Escolha outro.", ephemeral=True)
                return
        
        # Gera ID único para o time
        team_id = f"rematch_team_{len(data['teams']) + 1}_{user_id}"
        
        # Cria o time
        data["teams"][team_id] = {
            "name": nome,
            "captain": user_id,
            "members": [user_id],
            "created_at": datetime.now().isoformat(),
            "invites": [],
            "game_type": "REMATCH"
        }
        
        # Atualiza dados do jogador
        data["players"][user_id]["team_id"] = team_id
        
        save_rematch_data(data)
        
        # Calcula rank médio inicial
        update_rematch_team_average(team_id)
        
        # Cria canais e cargo do time (será implementado depois)
        # TODO: Implementar criação de canais REMATCH
        
        # Pega dados atualizados
        updated_team = get_rematch_team_data(team_id)
        
        embed = discord.Embed(
            title="✅ Time REMATCH Criado!",
            description=f"Time **{nome}** criado com sucesso!\nVocê é o capitão.",
            color=discord.Color.green()
        )
        embed.add_field(name="Membros", value="1/5", inline=True)
        embed.add_field(name="Rank Médio", value=updated_team.get("average_rank_name", "Calculando..."), inline=True)
        embed.add_field(name="Próximo passo", value="Use `/convidar_rematch @jogador` para convidar outros jogadores", inline=False)
        
        await interaction.followup.send(embed=embed, ephemeral=True)

    @app_commands.command(name="meu_time_rematch", description="Veja informações do seu time REMATCH")
    async def my_rematch_team(self, interaction: discord.Interaction):
        player = get_rematch_player_data(interaction.user.id)
        
        if not player or "team_id" not in player:
            await interaction.response.send_message("❌ Você não está em nenhum time REMATCH.", ephemeral=True)
            return
        
        team = get_rematch_team_data(player["team_id"])
        if not team:
            await interaction.response.send_message("❌ Erro: Time não encontrado.", ephemeral=True)
            return
        
        data = load_rematch_data()
        
        # Lista membros com ranks
        members_info = []
        for member_id in team["members"]:
            member_data = data["players"].get(member_id)
            if member_data:
                rank = member_data.get("rank", "Sem rank")
                is_captain = "👑" if member_id == team["captain"] else ""
                members_info.append(f"<@{member_id}> {is_captain} - {rank}")
        
        embed = discord.Embed(
            title=f"🔫 Time REMATCH: {team['name']}",
            color=discord.Color.blue()
        )
        embed.add_field(name="👥 Membros", value="\n".join(members_info) if members_info else "Nenhum", inline=False)
        embed.add_field(name="📊 Rank Médio", value=team.get("average_rank_name", "Calculando..."), inline=True)
        embed.add_field(name="👑 Capitão", value=f"<@{team['captain']}>", inline=True)
        embed.add_field(name="📅 Criado em", value=team["created_at"][:10], inline=True)
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @app_commands.command(name="dissolver_time_rematch", description="Dissolve seu time REMATCH (apenas capitão)")
    async def dissolve_rematch_team(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

        user_id = str(interaction.user.id)
        player_data = get_rematch_player_data(interaction.user.id)

        if not player_data or "team_id" not in player_data:
            await interaction.followup.send("❌ Você não está em nenhum time REMATCH.", ephemeral=True)
            return

        team_id = player_data["team_id"]
        team_data = get_rematch_team_data(team_id)

        if not team_data or team_data["captain"] != user_id:
            await interaction.followup.send("❌ Apenas o capitão pode dissolver o time.", ephemeral=True)
            return

        # Remove time dos dados
        data = load_rematch_data()
        team_name = team_data["name"]

        # Remove team_id de todos os membros
        for member_id in team_data["members"]:
            if member_id in data["players"] and "team_id" in data["players"][member_id]:
                del data["players"][member_id]["team_id"]

        # Remove o time
        del data["teams"][team_id]
        save_rematch_data(data)

        # Deleta canais do time
        rematch_channels_cog = interaction.client.get_cog("RematchChannels")
        if rematch_channels_cog:
            await rematch_channels_cog.delete_rematch_team_channels(interaction.guild, team_id)

        embed = discord.Embed(
            title="✅ Time Dissolvido",
            description=f"Time **{team_name}** foi dissolvido com sucesso.",
            color=discord.Color.green()
        )

        await interaction.followup.send(embed=embed, ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchRanksTeamsCog(bot), guild=discord.Object(id=config.GUILD_ID))
