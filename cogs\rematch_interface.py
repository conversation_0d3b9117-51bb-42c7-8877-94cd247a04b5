# cogs/rematch_interface.py

import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import <PERSON>ton, View, Select, Modal, TextInput
import config

# Ranks do REMATCH
REMATCH_RANKS = [
    ("<PERSON>", 1), ("<PERSON>+", 2),
    ("<PERSON><PERSON>", 3), ("<PERSON><PERSON>+", 4),
    ("<PERSON><PERSON>", 5), ("Ouro+", 6),
    ("<PERSON>latina", 7), ("Platina+", 8),
    ("<PERSON>amante", 9), ("<PERSON>amante+", 10),
    ("Mestre", 11), ("Mestre+", 12),
    ("Grão-Mestre", 13), ("Grão-Mestre+", 14),
    ("Challenger", 15)
]

class RematchRankSelect(Select):
    def __init__(self):
        # Cria opções para o select
        options = []
        for rank_name, rank_points in REMATCH_RANKS:
            options.append(discord.SelectOption(
                label=rank_name,
                value=str(rank_points),
                description=f"{rank_points} pontos"
            ))
        
        super().__init__(
            placeholder="Escolha seu rank atual no REMATCH...",
            options=options,
            min_values=1,
            max_values=1,
            custom_id="rematch_rank_select"
        )

    async def callback(self, interaction: discord.Interaction):
        rank_points = int(self.values[0])
        rank_name = next(name for name, points in REMATCH_RANKS if points == rank_points)
        
        # Registra o rank REMATCH
        from cogs.rematch_ranks_teams import register_rematch_player_rank
        success, message = await register_rematch_player_rank(interaction.user.id, rank_name, rank_points)
        
        if success:
            embed = discord.Embed(
                title="✅ Rank REMATCH Registrado!",
                description=f"Seu rank **{rank_name}** foi registrado no REMATCH!",
                color=discord.Color.red()
            )
            embed.add_field(
                name="📊 Próximos passos",
                value="• Agora você pode criar um time com o botão abaixo\n• Ou ser convidado para um time REMATCH existente",
                inline=False
            )
        else:
            embed = discord.Embed(
                title="❌ Erro",
                description=message,
                color=discord.Color.red()
            )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

class RematchRankRegistrationView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(RematchRankSelect())

class CreateRematchTeamModal(Modal):
    def __init__(self):
        super().__init__(title="Criar Novo Time REMATCH")
        
        self.team_name = TextInput(
            label="Nome do Time REMATCH",
            placeholder="Digite o nome do seu time REMATCH (máx. 20 caracteres)",
            max_length=20,
            required=True
        )
        self.add_item(self.team_name)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        
        # Verifica se tem rank REMATCH registrado
        from cogs.rematch_ranks_teams import load_rematch_data
        data = load_rematch_data()
        user_id = str(interaction.user.id)
        
        if user_id not in data["players"] or "rank" not in data["players"][user_id]:
            await interaction.followup.send("❌ Você precisa registrar seu rank REMATCH primeiro usando o menu acima.", ephemeral=True)
            return
        
        # Verifica se já está em um time REMATCH
        if "team_id" in data["players"][user_id]:
            await interaction.followup.send("❌ Você já está em um time REMATCH. Use `/sair_time_rematch` primeiro.", ephemeral=True)
            return
        
        # Verifica se o nome já existe
        for team in data["teams"].values():
            if team["name"].lower() == self.team_name.value.lower():
                await interaction.followup.send("❌ Já existe um time REMATCH com esse nome. Escolha outro.", ephemeral=True)
                return
        
        # Cria o time REMATCH
        from cogs.rematch_ranks_teams import create_rematch_team_for_user
        success, result = await create_rematch_team_for_user(interaction, self.team_name.value)
        
        if success:
            embed = discord.Embed(
                title="✅ Time REMATCH Criado!",
                description=f"Time **{self.team_name.value}** criado com sucesso!\nVocê é o capitão.",
                color=discord.Color.red()
            )
            
            if "channels" in result:
                embed.add_field(
                    name="🔫 Canais Criados",
                    value=f"• {result['channels']['manage_channel'].mention} - Gerenciamento\n• {result['channels']['chat_channel'].mention} - Chat\n• {result['channels']['voice_channel'].mention} - Voz",
                    inline=False
                )
            
            embed.add_field(
                name="📋 Próximos passos",
                value="• Use `/convidar_rematch @jogador` para convidar outros jogadores\n• Acesse seu canal de gerenciamento para mais opções\n• Entre nas filas 2v2, 3v3, 4v4 ou 5v5!",
                inline=False
            )
            
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(f"❌ Erro ao criar time REMATCH: {result}", ephemeral=True)

class CreateRematchTeamButton(Button):
    def __init__(self):
        super().__init__(
            label="🔫 Criar Meu Time REMATCH",
            style=discord.ButtonStyle.danger,
            emoji="🔫",
            custom_id="create_rematch_team_button"
        )

    async def callback(self, interaction: discord.Interaction):
        modal = CreateRematchTeamModal()
        await interaction.response.send_modal(modal)

class CreateRematchTeamView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(CreateRematchTeamButton())

class RematchInterfaceCog(commands.Cog, name="RematchInterface"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_ready(self):
        # Registra views persistentes
        if not hasattr(self.bot, '_rematch_interface_views_registered'):
            self.bot.add_view(RematchRankRegistrationView())
            self.bot.add_view(CreateRematchTeamView())
            self.bot._rematch_interface_views_registered = True
        print("Rematch Interface Cog is ready. Views registered.")

    @app_commands.command(name="setup_rank_interface_rematch", description="Cria interface de registro de rank REMATCH (Staff)")
    async def setup_rematch_rank_interface(self, interaction: discord.Interaction):
        # Verifica se é staff
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await interaction.response.defer()
        
        embed = discord.Embed(
            title="🔫 Registrar Rank - REMATCH",
            description="Registre seu rank atual no REMATCH para poder criar ou entrar em times!",
            color=discord.Color.red()
        )
        
        embed.add_field(
            name="🎯 Como funciona:",
            value="• Escolha seu rank atual no menu abaixo\n• Seu rank será registrado automaticamente\n• Depois você pode criar ou entrar em times REMATCH",
            inline=False
        )
        
        embed.add_field(
            name="🔄 Atualizar rank:",
            value="• Você pode usar este menu novamente para atualizar\n• Seu rank nos times será recalculado automaticamente",
            inline=False
        )
        
        embed.add_field(
            name="🎮 Filas disponíveis:",
            value="• **2v2** - 4 jogadores\n• **3v3** - 6 jogadores\n• **4v4** - 8 jogadores\n• **5v5** - 10 jogadores (ranking)",
            inline=False
        )
        
        embed.set_footer(text="Sistema de Ranks REMATCH • WantedQueue")
        
        view = RematchRankRegistrationView()
        await interaction.channel.send(embed=embed, view=view)
        
        await interaction.followup.send("✅ Interface de registro de rank REMATCH criada!", ephemeral=True)

    @app_commands.command(name="setup_team_interface_rematch", description="Cria interface de criação de times REMATCH (Staff)")
    async def setup_rematch_team_interface(self, interaction: discord.Interaction):
        # Verifica se é staff
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
        
        await interaction.response.defer()
        
        embed = discord.Embed(
            title="🔫 Criar Time - REMATCH",
            description="Crie seu próprio time REMATCH e comece a apostar!",
            color=discord.Color.red()
        )
        
        embed.add_field(
            name="📋 Requisitos:",
            value="• Ter rank REMATCH registrado\n• Não estar em outro time REMATCH\n• Nome único (máx. 20 caracteres)",
            inline=False
        )
        
        embed.add_field(
            name="🎯 Ao criar seu time você ganha:",
            value="• Canais privados do time\n• Cargo exclusivo no Discord\n• Poder de convidar jogadores\n• Acesso às filas 2v2, 3v3, 4v4, 5v5",
            inline=False
        )
        
        embed.add_field(
            name="👑 Como capitão você pode:",
            value="• Convidar e remover jogadores\n• Renomear o time\n• Publicar vagas\n• Dissolver o time",
            inline=False
        )
        
        embed.add_field(
            name="🏆 Ranking:",
            value="• Apenas partidas **5v5** contam para o ranking\n• Sistema de pontos: +3 vitória, 0 derrota\n• Mínimo 2 partidas para aparecer",
            inline=False
        )
        
        embed.set_footer(text="Sistema de Times REMATCH • WantedQueue")
        
        view = CreateRematchTeamView()
        await interaction.channel.send(embed=embed, view=view)
        
        await interaction.followup.send("✅ Interface de criação de times REMATCH criada!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchInterfaceCog(bot), guild=discord.Object(id=config.GUILD_ID))
