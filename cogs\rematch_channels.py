# cogs/rematch_channels.py

import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime
import config

# Arquivo separado para canais REMATCH
REMATCH_CHANNELS_FILE = "rematch_team_channels.json"

def load_rematch_team_channels_data():
    if os.path.exists(REMATCH_CHANNELS_FILE):
        with open(REMATCH_CHANNELS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_rematch_team_channels_data(data):
    with open(REMATCH_CHANNELS_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

class RematchChannelsCog(commands.Cog, name="RematchChannels"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    async def create_rematch_team_channels(self, guild, team_name: str, team_id: str, captain_id: int):
        """Cria canais privados para um time REMATCH."""
        try:
            # Pega a categoria REMATCH configurada
            rematch_category = None
            if config.REMATCH_TEAMS_CATEGORY_ID:
                rematch_category = guild.get_channel(config.REMATCH_TEAMS_CATEGORY_ID)

            if not rematch_category:
                # Se não encontrar a categoria configurada, procura por nome
                for category in guild.categories:
                    if "rematch" in category.name.lower():
                        rematch_category = category
                        break

                if not rematch_category:
                    # Se ainda não encontrar, cria a categoria
                    rematch_category = await guild.create_category(
                        "🔫 TIMES REMATCH",
                        reason="Categoria para times REMATCH"
                    )
                    print(f"⚠️ REMATCH: Categoria criada automaticamente. Configure REMATCH_TEAMS_CATEGORY_ID = {rematch_category.id} no config.py")
            
            # Cria o cargo do time
            team_role = await guild.create_role(
                name=team_name,
                color=discord.Color.red(),
                reason=f"Cargo do time REMATCH {team_name}"
            )
            
            # Permissões para os canais do time
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(read_messages=False),
                team_role: discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    connect=True,
                    speak=True
                ),
                guild.get_role(config.STAFF_ROLE_ID): discord.PermissionOverwrite(
                    read_messages=True,
                    send_messages=True,
                    manage_messages=True
                ) if config.STAFF_ROLE_ID else None
            }
            
            # Remove None do overwrites
            overwrites = {k: v for k, v in overwrites.items() if v is not None}
            
            # Cria canal de gerenciamento
            manage_channel_name = f"{team_name.lower().replace(' ', '-')}-gerenciar"
            manage_channel = await guild.create_text_channel(
                manage_channel_name,
                category=rematch_category,
                overwrites=overwrites,
                reason=f"Canal de gerenciamento do time REMATCH {team_name}"
            )
            
            # Cria canal de chat
            chat_channel_name = f"{team_name.lower().replace(' ', '-')}-chat"
            chat_channel = await guild.create_text_channel(
                chat_channel_name,
                category=rematch_category,
                overwrites=overwrites,
                reason=f"Canal de chat do time REMATCH {team_name}"
            )
            
            # Cria canal de voz
            voice_channel = await guild.create_voice_channel(
                team_name,
                category=rematch_category,
                overwrites=overwrites,
                reason=f"Canal de voz do time REMATCH {team_name}"
            )
            
            # Salva dados dos canais
            channels_data = load_rematch_team_channels_data()
            channels_data[team_id] = {
                "team_name": team_name,
                "captain_id": captain_id,
                "role_id": team_role.id,
                "manage_channel_id": manage_channel.id,
                "chat_channel_id": chat_channel.id,
                "voice_channel_id": voice_channel.id,
                "created_at": datetime.now().isoformat()
            }
            save_rematch_team_channels_data(channels_data)
            
            # Envia mensagem fixa no canal de gerenciamento
            await self.send_rematch_management_message(manage_channel, team_name, team_id)
            
            print(f"✅ REMATCH: Canais criados para time {team_name}")
            
            return True, {
                "role": team_role,
                "manage_channel": manage_channel,
                "chat_channel": chat_channel,
                "voice_channel": voice_channel
            }
            
        except Exception as e:
            print(f"❌ REMATCH: Erro ao criar canais: {e}")
            return False, str(e)

    async def send_rematch_management_message(self, channel, team_name: str, team_id: str):
        """Envia mensagem fixa de gerenciamento no canal do time REMATCH."""
        try:
            from cogs.rematch_ranks_teams import get_rematch_team_data, load_rematch_data
            
            team_data = get_rematch_team_data(team_id)
            if not team_data:
                return
            
            data = load_rematch_data()
            
            # Lista membros com ranks
            members_info = []
            for member_id in team_data["members"]:
                member_data = data["players"].get(member_id)
                if member_data:
                    rank = member_data.get("rank", "Sem rank")
                    is_captain = "👑" if member_id == team_data["captain"] else ""
                    members_info.append(f"<@{member_id}> {is_captain} - {rank}")
            
            embed = discord.Embed(
                title=f"🔫 Gerenciamento - {team_name}",
                description="Canal privado de gerenciamento do time REMATCH",
                color=discord.Color.red(),
                timestamp=datetime.now()
            )
            
            embed.add_field(
                name="👥 Membros do Time",
                value="\n".join(members_info) if members_info else "Nenhum",
                inline=False
            )
            
            embed.add_field(
                name="📊 Informações",
                value=f"**Rank Médio:** {team_data.get('average_rank_name', 'Calculando...')}\n**Capitão:** <@{team_data['captain']}>",
                inline=False
            )
            
            embed.add_field(
                name="👥 Comandos de Membros",
                value=(
                    "`/convidar_rematch @jogador` - Convida um jogador\n"
                    "`/remover_rematch @jogador` - Remove um jogador\n"
                    "`/promover_rematch @jogador` - Promove a capitão\n"
                    "`/meu_time_rematch` - Informações do time"
                ),
                inline=False
            )
            
            embed.add_field(
                name="⚙️ Comandos de Gerenciamento",
                value=(
                    "`/renomear_time_rematch` - Renomeia o time\n"
                    "`/recalcular_rank_rematch` - Recalcula rank médio\n"
                    "`/dissolver_time_rematch` - Dissolve o time\n"
                    "`/publicar_vaga_rematch` - Publica vaga no #procurar-times"
                ),
                inline=False
            )
            
            embed.add_field(
                name="🎯 Filas Disponíveis",
                value="• **2v2** - R$5, R$10, R$25, R$50, R$100\n• **3v3** - R$5, R$10, R$25, R$50, R$100\n• **4v4** - R$5, R$10, R$25, R$50, R$100\n• **5v5** - R$5, R$10, R$25, R$50, R$100 (ranking)",
                inline=False
            )
            
            embed.set_footer(text="Sistema de Times REMATCH • WantedQueue")
            
            message = await channel.send(embed=embed)
            await message.pin()
            
        except Exception as e:
            print(f"❌ REMATCH: Erro ao enviar mensagem de gerenciamento: {e}")

    async def delete_rematch_team_channels(self, guild, team_id: str):
        """Deleta canais de um time REMATCH."""
        try:
            channels_data = load_rematch_team_channels_data()
            
            if team_id not in channels_data:
                return True
            
            team_info = channels_data[team_id]
            
            # Deleta canais
            for channel_type in ["manage_channel_id", "chat_channel_id", "voice_channel_id"]:
                channel_id = team_info.get(channel_type)
                if channel_id:
                    channel = guild.get_channel(channel_id)
                    if channel:
                        await channel.delete(reason="Time REMATCH dissolvido")
            
            # Deleta cargo
            role_id = team_info.get("role_id")
            if role_id:
                role = guild.get_role(role_id)
                if role:
                    await role.delete(reason="Time REMATCH dissolvido")
            
            # Remove dos dados
            del channels_data[team_id]
            save_rematch_team_channels_data(channels_data)
            
            print(f"✅ REMATCH: Canais deletados para time {team_info.get('team_name', 'Desconhecido')}")
            return True
            
        except Exception as e:
            print(f"❌ REMATCH: Erro ao deletar canais: {e}")
            return False

    async def update_rematch_management_message(self, guild, team_id: str):
        """Atualiza a mensagem de gerenciamento do time REMATCH."""
        try:
            channels_data = load_rematch_team_channels_data()
            
            if team_id not in channels_data:
                return
            
            manage_channel_id = channels_data[team_id]["manage_channel_id"]
            manage_channel = guild.get_channel(manage_channel_id)
            
            if not manage_channel:
                return
            
            # Encontra a mensagem fixada
            async for message in manage_channel.history(limit=50):
                if message.author == guild.me and message.pinned and message.embeds:
                    if "Gerenciamento -" in message.embeds[0].title:
                        # Atualiza a mensagem
                        team_name = channels_data[team_id]["team_name"]
                        await self.send_rematch_management_message(manage_channel, team_name, team_id)
                        await message.delete()
                        break
                        
        except Exception as e:
            print(f"❌ REMATCH: Erro ao atualizar mensagem de gerenciamento: {e}")

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchChannelsCog(bot), guild=discord.Object(id=config.GUILD_ID))
