# cogs/rematch_ranking.py

import discord
from discord.ext import commands
from discord import app_commands
import json
import os
from datetime import datetime
import config

# Configurações
REMATCH_RANKING_CHANNEL_ID = config.REMATCH_RANKING_CHANNEL_ID
REMATCH_RANKING_FILE = "rematch_ranking_data.json"

def load_rematch_ranking_data():
    if os.path.exists(REMATCH_RANKING_FILE):
        with open(REMATCH_RANKING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_rematch_ranking_data(data):
    with open(REMATCH_RANKING_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def get_rematch_team_ranking_data(team_id: str):
    """Pega dados de ranking de um time REMATCH específico."""
    data = load_rematch_ranking_data()
    if team_id not in data:
        data[team_id] = {
            "wins": 0,
            "losses": 0,
            "points": 0,
            "current_streak": 0,
            "streak_type": "none",  # "win", "loss", "none"
            "last_match": None,
            "total_matches": 0
        }
        save_rematch_ranking_data(data)
    return data[team_id]

def update_rematch_team_stats(team_id: str, result: str):
    """Atualiza estatísticas de um time REMATCH após uma partida.
    result: 'win', 'loss'
    """
    data = load_rematch_ranking_data()
    team_stats = get_rematch_team_ranking_data(team_id)
    
    # Atualiza estatísticas básicas
    if result == "win":
        team_stats["wins"] += 1
        team_stats["points"] += 3
        # Atualiza sequência
        if team_stats["streak_type"] == "win":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "win"
            
    elif result == "loss":
        team_stats["losses"] += 1
        # Pontos não mudam em derrota
        # Atualiza sequência
        if team_stats["streak_type"] == "loss":
            team_stats["current_streak"] += 1
        else:
            team_stats["current_streak"] = 1
            team_stats["streak_type"] = "loss"
    
    team_stats["total_matches"] += 1
    team_stats["last_match"] = datetime.now().isoformat()
    
    data[team_id] = team_stats
    save_rematch_ranking_data(data)
    
    return team_stats

def calculate_rematch_win_rate(team_stats):
    """Calcula taxa de vitória REMATCH."""
    total = team_stats["total_matches"]
    if total == 0:
        return 0.0
    return (team_stats["wins"] / total) * 100

def get_rematch_ranking_table():
    """Gera tabela de ranking REMATCH ordenada por pontos."""
    data = load_rematch_ranking_data()
    
    # Filtra times com pelo menos 2 jogos
    qualified_teams = {}
    for team_id, stats in data.items():
        if stats["total_matches"] >= 2:
            qualified_teams[team_id] = stats
    
    # Ordena por pontos (desc), depois por taxa de vitória (desc)
    sorted_teams = sorted(
        qualified_teams.items(),
        key=lambda x: (x[1]["points"], calculate_rematch_win_rate(x[1])),
        reverse=True
    )
    
    return sorted_teams

async def update_rematch_ranking_channel(bot):
    """Atualiza a mensagem de ranking REMATCH no canal."""
    try:
        channel = bot.get_channel(REMATCH_RANKING_CHANNEL_ID)
        if not channel:
            return
        
        # Pega dados dos times REMATCH
        from cogs.rematch_ranks_teams import load_rematch_data
        teams_data = load_rematch_data()
        
        ranking = get_rematch_ranking_table()
        
        # Cria embed da tabela de classificação
        if not ranking:
            ranking_embed = discord.Embed(
                title="🔫 Ranking de Times - REMATCH",
                description="Nenhum time qualificado ainda.\n*Mínimo: 2 partidas jogadas*",
                color=discord.Color.red()
            )
        else:
            ranking_embed = discord.Embed(
                title="🔫 Ranking de Times - REMATCH",
                description="*Atualizado automaticamente após cada partida 5v5*",
                color=discord.Color.red()
            )
            
            # Adiciona top 10 com formatação melhorada
            ranking_text = ""
            for i, (team_id, stats) in enumerate(ranking[:10], 1):
                # Pega nome do time
                team_name = teams_data["teams"].get(team_id, {}).get("name", "Time Desconhecido")
                
                # Emoji de posição
                if i == 1:
                    pos_emoji = "🥇"
                elif i == 2:
                    pos_emoji = "🥈"
                elif i == 3:
                    pos_emoji = "🥉"
                else:
                    pos_emoji = f"**{i}º**"
                
                win_rate = calculate_rematch_win_rate(stats)
                
                # Formatação limpa
                ranking_text += (
                    f"{pos_emoji} **{team_name}**\n"
                    f" {stats['points']} pts ••• {stats['wins']}V-{stats['losses']}D • {win_rate:.1f}%\n\n"
                )
            
            ranking_embed.add_field(name="📈 Classificação", value=ranking_text, inline=False)
        
        ranking_embed.set_footer(text=f"\nÚltima atualização: {datetime.now().strftime('%d/%m/%Y às %H:%M')}")
        
        # Cria embed dos critérios (sempre)
        criteria_embed = discord.Embed(
            title="📋 Como Funciona o Ranking REMATCH",
            color=discord.Color.orange()
        )
        
        criteria_embed.add_field(
            name="🎯 Sistema de Pontuação",
            value="🏆 **Vitória:** +3 pontos\n💔 **Derrota:** 0 pontos",
            inline=True
        )
        
        criteria_embed.add_field(
            name="📊 Critérios de Classificação",
            value="• Ordenado por **pontos totais**\n• Desempate por **taxa de vitória**\n• Mínimo **2 partidas** para aparecer",
            inline=True
        )
        
        criteria_embed.add_field(
            name="⚡ Atualização",
            value="• **Apenas partidas 5v5** contam para o ranking\n• Detecta times dos jogadores\n• Precisa de **2 times REMATCH** na partida",
            inline=False
        )
        
        # Procura mensagens existentes de ranking
        ranking_messages = []
        criteria_messages = []
        
        async for message in channel.history(limit=100):
            if message.author == bot.user and message.embeds:
                if "Ranking de Times - REMATCH" in message.embeds[0].title:
                    ranking_messages.append(message)
                elif "Como Funciona o Ranking REMATCH" in message.embeds[0].title:
                    criteria_messages.append(message)
        
        # Atualiza ou cria mensagem de ranking
        if ranking_messages:
            await ranking_messages[0].edit(embed=ranking_embed)
            # Deleta mensagens antigas de ranking
            for old_message in ranking_messages[1:]:
                try:
                    await old_message.delete()
                except:
                    pass
        else:
            # Cria nova mensagem de ranking
            ranking_msg = await channel.send(embed=ranking_embed)
            try:
                await ranking_msg.pin()
            except:
                pass
        
        # Atualiza ou cria mensagem de critérios
        if criteria_messages:
            await criteria_messages[0].edit(embed=criteria_embed)
            # Deleta mensagens antigas de critérios
            for old_message in criteria_messages[1:]:
                try:
                    await old_message.delete()
                except:
                    pass
        else:
            # Cria nova mensagem de critérios
            await channel.send(embed=criteria_embed)
            
    except Exception as e:
        print(f"Erro ao atualizar ranking REMATCH: {e}")

def find_rematch_teams_from_players(players_list):
    """Encontra times REMATCH dos jogadores na partida."""
    print(f"🔍 REMATCH RANKING DEBUG: Procurando times para jogadores: {players_list}")
    
    from cogs.rematch_ranks_teams import load_rematch_data
    teams_data = load_rematch_data()
    
    print(f"🔍 REMATCH RANKING DEBUG: Total de times REMATCH registrados: {len(teams_data.get('teams', {}))}")
    print(f"🔍 REMATCH RANKING DEBUG: Total de jogadores REMATCH registrados: {len(teams_data.get('players', {}))}")
    
    found_teams = {}
    
    for player_id in players_list:
        player_str = str(player_id)
        player_data = teams_data["players"].get(player_str)
        
        print(f"🔍 REMATCH RANKING DEBUG: Jogador {player_id} -> Dados: {player_data}")
        
        if player_data and "team_id" in player_data:
            team_id = player_data["team_id"]
            team_info = teams_data["teams"].get(team_id)
            
            print(f"🔍 REMATCH RANKING DEBUG: Time encontrado {team_id} -> {team_info}")
            
            if team_info and team_info.get("game_type") == "REMATCH":
                if team_id not in found_teams:
                    found_teams[team_id] = {
                        "name": team_info["name"],
                        "players": []
                    }
                found_teams[team_id]["players"].append(player_id)
    
    print(f"🔍 REMATCH RANKING DEBUG: Times REMATCH encontrados: {found_teams}")
    return found_teams

def register_rematch_team_victory(winner_team_id, loser_team_id, bet_value):
    """Registra vitória/derrota dos times REMATCH e atualiza ranking."""
    try:
        # Atualiza estatísticas
        update_rematch_team_stats(winner_team_id, "win")
        update_rematch_team_stats(loser_team_id, "loss")
        
        # Pega nomes dos times
        from cogs.rematch_ranks_teams import load_rematch_data
        teams_data = load_rematch_data()
        
        winner_name = teams_data["teams"].get(winner_team_id, {}).get("name", "Time Desconhecido")
        loser_name = teams_data["teams"].get(loser_team_id, {}).get("name", "Time Desconhecido")
        
        print(f"🔫 REMATCH RANKING ATUALIZADO: {winner_name} venceu {loser_name} (Aposta: {bet_value})")
        
        return {
            "success": True,
            "winner_name": winner_name,
            "loser_name": loser_name
        }
        
    except Exception as e:
        print(f"❌ Erro ao registrar vitória REMATCH: {e}")
        return {"success": False, "error": str(e)}

class RematchRankingCog(commands.Cog, name="RematchRanking"):
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        print("Rematch Ranking Cog initialized")

    @app_commands.command(name="setup_ranking_rematch", description="Cria mensagem fixa de ranking REMATCH (Staff)")
    async def setup_rematch_ranking(self, interaction: discord.Interaction):
        # Verifica se é staff
        if not any(role.id == config.STAFF_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("❌ Apenas staff pode usar este comando.", ephemeral=True)
            return
            
        await interaction.response.defer()
        
        # Força criação da mensagem fixa
        await update_rematch_ranking_channel(self.bot)
        
        await interaction.followup.send("✅ Mensagem fixa de ranking REMATCH criada/atualizada!", ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(RematchRankingCog(bot), guild=discord.Object(id=config.GUILD_ID))
