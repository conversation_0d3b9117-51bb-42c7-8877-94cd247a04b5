# 🏆 Sistema de Ranks e Times - Guia Completo

## ✅ **Sistema Implementado com Sucesso!**

### 🎯 **Funcionalidades Principais:**
- ✅ **Sistema de Ranks Valorant** (Iron 1 até Radiant)
- ✅ **Sistema de Times** (CRUD completo)
- ✅ **Validação nas Filas** (rank + time completo obrigatório)
- ✅ **Rank Médio Automático** dos times
- ✅ **Integração Total** com sistema de apostas existente

---

## 📋 **Comandos Disponíveis**

### **🎖️ Comandos de Rank:**
```
/registrar_rank     # Registra/atualiza seu rank (dropdown com todos os ranks)
/meu_rank          # Mostra seu rank atual
/rank @jogador     # Vê o rank de outro jogador
```

### **👥 Comandos de Times:**
```
/criar_time <nome>     # Cria um novo time (você vira capitão)
/meu_time             # Informações completas do seu time
/convidar @jogador    # Convida jogador para o time (só capitão)
/aceitar_convite      # Aceita convite pendente
/sair_time           # Sai do time atual
/remover @jogador    # Remove jogador do time (só capitão)
/dissolver_time      # Dissolve o time (só capitão)
```

---

## 🎮 **Fluxo de Experiência**

### **1. Novo Jogador:**
```
1️⃣ /registrar_rank → Escolhe rank no dropdown
2️⃣ /criar_time "Meu Time" → Cria time ou procura um
3️⃣ /convidar @amigo1 @amigo2 @amigo3 @amigo4 → Convida 4 jogadores
4️⃣ Aguarda todos aceitarem
5️⃣ Quando time tem 5 → pode jogar nas filas!
```

### **2. Entrando nas Filas:**
```
❌ Sem rank → "Você precisa registrar seu rank primeiro: /registrar_rank"
❌ Sem time → "Você precisa estar em um time. Use /criar_time"
❌ Time incompleto → "Seu time 'Nome' precisa ter 5 jogadores (atual: 3/5)"
❌ Membros sem rank → "Alguns membros não têm rank registrado"
✅ Tudo OK → Entra na fila normalmente
```

---

## 🏆 **Sistema de Ranks**

### **Ranks e Pontuações:**
```
Iron 1-3      → 1-3 pontos
Bronze 1-3    → 4-6 pontos  
Silver 1-3    → 7-9 pontos
Gold 1-3      → 10-12 pontos
Platinum 1-3  → 13-15 pontos
Diamond 1-3   → 16-18 pontos
Ascendant 1-3 → 19-21 pontos
Immortal 1-3  → 22-24 pontos
Radiant       → 25 pontos
```

### **Cálculo do Rank Médio:**
```
Exemplo: Time com Gold 2, Plat 1, Silver 3, Gold 1, Gold 3
Pontos: 11 + 13 + 9 + 10 + 12 = 55 pontos
Média: 55 ÷ 5 = 11 pontos = Gold 2
```

---

## 👥 **Sistema de Times**

### **Características:**
- ✅ **Nomes únicos**: Não pode ter 2 times "Fire Dragons"
- ✅ **Máximo 20 caracteres** no nome
- ✅ **1 time por pessoa**: Deve sair do atual para entrar em outro
- ✅ **Times mistos**: Qualquer combinação de ranks
- ✅ **CRUD completo**: Criar, convidar, remover, dissolver

### **Hierarquia:**
- **👑 Capitão**: Pode convidar, remover, dissolver time
- **👤 Membros**: Podem sair do time
- **⏳ Convidados**: Podem aceitar/recusar convites

### **Regras:**
- Capitão não pode sair se há outros membros (deve promover alguém ou dissolver)
- Time precisa ter exatamente 5 jogadores para jogar
- Todos os 5 devem ter rank registrado

---

## 🎯 **Integração com Filas**

### **Antes (sem sistema):**
```
[APOSTA 5x5 - 50 R$]
Jogadores na Fila:
@user1
@user2
```

### **Agora (com sistema):**
```
[APOSTA 5x5 - 50 R$]
⚠️ Requisitos: Rank registrado + Time completo (5 jogadores)

Times na Fila:
@user1 - Fire Dragons (Rank Médio: Gold 3)
@user2 - Ice Wolves (Rank Médio: Platinum 1)
```

### **Canal de Mediação:**
```
🚨 NOVA MEDIAÇÃO - APOSTA 5x5 - 50 R$ 🚨

Time 1: Fire Dragons (Gold 3)
@player1 @player2

Time 2: Ice Wolves (Plat 1)  
@player3 @player4

Rank Médio da Partida: Gold 3 vs Plat 1
```

---

## 💾 **Armazenamento de Dados**

### **Arquivo: `ranks_teams_data.json`**
```json
{
  "players": {
    "user_id": {
      "rank": "Gold 2",
      "rank_points": 11,
      "team_id": "team_123",
      "updated_at": "2025-01-01T10:00:00"
    }
  },
  "teams": {
    "team_123": {
      "name": "Fire Dragons",
      "captain": "user_id",
      "members": ["user1", "user2", "user3", "user4", "user5"],
      "average_rank": 12.4,
      "average_rank_name": "Gold 3",
      "created_at": "2025-01-01T10:00:00",
      "invites": ["pending_user_id"]
    }
  }
}
```

---

## 🔧 **Arquivos Criados/Modificados**

### **Novos Arquivos:**
- ✅ `cogs/ranks_teams.py` - Sistema completo de ranks e times
- ✅ `utils/team_validation.py` - Validações para filas
- ✅ `utils/__init__.py` - Módulo Python
- ✅ `ranks_teams_data.json` - Banco de dados (criado automaticamente)

### **Modificados:**
- ✅ `main.py` - Carrega novo cog
- ✅ `cogs/multi_queues.py` - Validação e exibição de times

---

## 🚀 **Como Testar**

### **1. Registrar Rank:**
```
/registrar_rank → Escolher "Gold 2" → ✅ Rank registrado!
/meu_rank → Mostra "Gold 2 (11 pontos)"
```

### **2. Criar Time:**
```
/criar_time Fire Dragons → ✅ Time criado! Você é o capitão
/meu_time → Mostra info do time (1/5 membros)
```

### **3. Convidar Jogadores:**
```
/convidar @amigo1 → ✅ Convite enviado!
(Amigo usa /aceitar_convite)
/meu_time → Mostra 2/5 membros
```

### **4. Testar Fila:**
```
!setup_5v5 → Cria filas no canal
(Clica "Entrar na fila" com time incompleto)
❌ "Seu time 'Fire Dragons' precisa ter 5 jogadores (atual: 2/5)"
```

---

## ⚠️ **Importante**

### **Para Administradores:**
1. **Backup dos dados**: O arquivo `ranks_teams_data.json` contém todos os dados
2. **Permissões**: Comandos são públicos, qualquer um pode usar
3. **Moderação**: Staff pode usar comandos de admin se necessário

### **Para Jogadores:**
1. **Honestidade**: Sistema confia no rank informado
2. **Comunicação**: Coordenem-se para formar times
3. **Paciência**: Times levam tempo para formar

O sistema está 100% funcional e integrado! 🎉
