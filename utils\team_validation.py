# utils/team_validation.py

import json
import os

DATA_FILE = "ranks_teams_data.json"

def load_data():
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {"players": {}, "teams": {}}

def validate_player_for_queue(user_id):
    """
    Valida se um jogador pode entrar na fila.
    Retorna: (pode_entrar: bool, mensagem_erro: str, dados_time: dict)
    """
    data = load_data()
    user_id_str = str(user_id)
    
    # Verifica se tem rank registrado
    player = data["players"].get(user_id_str)
    if not player or "rank" not in player:
        return False, "❌ Você precisa registrar seu rank primeiro: `/registrar_rank`", None
    
    # Verifica se está em um time
    if "team_id" not in player:
        return False, "❌ Você precisa estar em um time. Use `/criar_time` ou procure um time.", None
    
    # Verifica se o time existe
    team_id = player["team_id"]
    team = data["teams"].get(team_id)
    if not team:
        return False, "❌ Erro: Seu time não foi encontrado. Contate um administrador.", None
    
    # Verifica se o time tem 5 jogadores
    if len(team["members"]) < 1:
        return False, f"❌ Seu time '{team['name']}' precisa ter 5 jogadores (atual: {len(team['members'])}/5)", None
    
    # Verifica se todos os membros têm rank
    missing_ranks = []
    for member_id in team["members"]:
        member_data = data["players"].get(member_id)
        if not member_data or "rank" not in member_data:
            missing_ranks.append(member_id)
    
    if missing_ranks:
        return False, f"❌ Alguns membros do seu time não têm rank registrado. Todos devem usar `/registrar_rank`", None
    
    # Tudo OK!
    return True, "", team

def get_team_info_for_queue(team_data):
    """
    Retorna informações formatadas do time para exibir na fila.
    """
    if not team_data:
        return "Time não encontrado"
    
    team_name = team_data["name"]
    avg_rank = team_data.get("average_rank_name", "Calculando...")
    
    return f"{team_name} (Rank Médio: {avg_rank})"

def get_team_members_mentions(team_data, bot):
    """
    Retorna mentions dos membros do time.
    """
    if not team_data:
        return []
    
    mentions = []
    for member_id in team_data["members"]:
        user = bot.get_user(int(member_id))
        if user:
            mentions.append(user.mention)
        else:
            mentions.append(f"<@{member_id}>")
    
    return mentions
